"""
系统集成测试
"""
import pytest
import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.data.data_processor import data_processor
from src.models.model_manager import model_manager
from src.trading.risk_manager import risk_manager, Position
from src.trading.execution_engine import execution_engine, TradingSignal, OrderSide, OrderType
from src.backtesting.backtest_engine import backtest_engine


class TestDataProcessor:
    """测试数据处理器"""
    
    def test_clean_data(self):
        """测试数据清洗"""
        # 创建测试数据
        dates = pd.date_range('2023-01-01', periods=100, freq='1min')
        df = pd.DataFrame({
            'open': np.random.uniform(100, 110, 100),
            'high': np.random.uniform(110, 120, 100),
            'low': np.random.uniform(90, 100, 100),
            'close': np.random.uniform(100, 110, 100),
            'volume': np.random.randint(1000, 10000, 100)
        }, index=dates)
        
        # 添加一些异常值和缺失值
        df.iloc[10] = np.nan
        df.iloc[20, 0] = 1000  # 异常值
        
        # 清洗数据
        cleaned_df = data_processor.clean_data(df)
        
        # 验证结果
        assert not cleaned_df.isnull().any().any(), "清洗后不应有缺失值"
        assert len(cleaned_df) > 0, "清洗后应有数据"
        assert all(cleaned_df['high'] >= cleaned_df['low']), "高价应大于等于低价"
    
    def test_calculate_technical_indicators(self):
        """测试技术指标计算"""
        # 创建测试数据
        dates = pd.date_range('2023-01-01', periods=100, freq='1min')
        df = pd.DataFrame({
            'open': np.random.uniform(100, 110, 100),
            'high': np.random.uniform(110, 120, 100),
            'low': np.random.uniform(90, 100, 100),
            'close': np.random.uniform(100, 110, 100),
            'volume': np.random.randint(1000, 10000, 100)
        }, index=dates)
        
        # 计算技术指标
        result_df = data_processor.calculate_technical_indicators(df)
        
        # 验证结果
        assert 'SMA_5' in result_df.columns, "应包含SMA_5指标"
        assert 'RSI' in result_df.columns, "应包含RSI指标"
        assert 'MACD' in result_df.columns, "应包含MACD指标"
        assert 'BB_Upper' in result_df.columns, "应包含布林带上轨"


class TestRiskManager:
    """测试风险管理器"""
    
    def setup_method(self):
        """测试前设置"""
        self.risk_mgr = risk_manager
        self.risk_mgr.current_capital = 100000
        self.risk_mgr.positions.clear()
    
    def test_calculate_position_size(self):
        """测试仓位计算"""
        symbol = "AAPL"
        price = 150.0
        confidence = 0.8
        
        quantity = self.risk_mgr.calculate_position_size(symbol, price, confidence)
        
        assert quantity > 0, "仓位数量应大于0"
        assert quantity * price <= self.risk_mgr.current_capital * self.risk_mgr.max_position_size, "不应超过最大仓位限制"
    
    def test_open_position(self):
        """测试开仓"""
        symbol = "AAPL"
        quantity = 100
        price = 150.0
        side = "long"
        
        success = self.risk_mgr.open_position(symbol, quantity, price, side)
        
        assert success, "开仓应成功"
        assert symbol in self.risk_mgr.positions, "应创建仓位"
        assert self.risk_mgr.positions[symbol].quantity == quantity, "仓位数量应正确"
    
    def test_close_position(self):
        """测试平仓"""
        # 先开仓
        symbol = "AAPL"
        quantity = 100
        price = 150.0
        side = "long"
        
        self.risk_mgr.open_position(symbol, quantity, price, side)
        
        # 平仓
        close_price = 155.0
        success = self.risk_mgr.close_position(symbol, quantity, close_price)
        
        assert success, "平仓应成功"
        assert symbol not in self.risk_mgr.positions, "仓位应被删除"
    
    def test_risk_limits(self):
        """测试风险限制"""
        symbol = "AAPL"
        quantity = 10000  # 过大的数量
        price = 150.0
        side = "long"
        
        risk_ok, msg = self.risk_mgr.check_risk_limits(symbol, quantity, price, side)
        
        assert not risk_ok, "应拒绝过大的交易"
        assert "超过最大限制" in msg, "应提示超过限制"


class TestExecutionEngine:
    """测试交易执行引擎"""
    
    def setup_method(self):
        """测试前设置"""
        self.engine = execution_engine
        self.engine.set_live_trading(False)  # 模拟模式
    
    def test_create_order(self):
        """测试创建订单"""
        order = self.engine.create_order(
            symbol="AAPL",
            side=OrderSide.BUY,
            quantity=100,
            order_type=OrderType.MARKET
        )
        
        assert order is not None, "应创建订单"
        assert order.symbol == "AAPL", "股票代码应正确"
        assert order.side == OrderSide.BUY, "方向应正确"
        assert order.quantity == 100, "数量应正确"
    
    def test_submit_order(self):
        """测试提交订单"""
        order = self.engine.create_order(
            symbol="AAPL",
            side=OrderSide.BUY,
            quantity=10,  # 小数量避免风险限制
            order_type=OrderType.MARKET,
            price=150.0
        )
        
        success = self.engine.submit_order(order)
        
        assert success, "订单提交应成功"
    
    def test_trading_signal_processing(self):
        """测试交易信号处理"""
        signal = TradingSignal(
            symbol="AAPL",
            action="BUY",
            confidence=0.8,
            price=150.0,
            model_name="test_model"
        )
        
        order = self.engine.process_trading_signal(signal)
        
        # 在模拟模式下，应该能处理信号
        # 实际结果取决于风险管理设置


class TestBacktestEngine:
    """测试回测引擎"""
    
    def test_backtest_basic(self):
        """测试基本回测功能"""
        engine = backtest_engine
        
        start_date = datetime.now() - timedelta(days=30)
        end_date = datetime.now() - timedelta(days=1)
        
        # 运行回测
        result = engine.run_backtest(
            symbol="AAPL",
            start_date=start_date,
            end_date=end_date,
            model_config={'model_type': 'random_forest'}
        )
        
        assert result is not None, "应返回回测结果"
        assert hasattr(result, 'total_return'), "应包含总收益率"
        assert hasattr(result, 'sharpe_ratio'), "应包含夏普比率"
        assert hasattr(result, 'max_drawdown'), "应包含最大回撤"


class TestSystemIntegration:
    """系统集成测试"""
    
    @pytest.mark.asyncio
    async def test_end_to_end_workflow(self):
        """测试端到端工作流"""
        # 1. 数据处理
        dates = pd.date_range('2023-01-01', periods=1000, freq='1min')
        df = pd.DataFrame({
            'open': np.random.uniform(100, 110, 1000),
            'high': np.random.uniform(110, 120, 1000),
            'low': np.random.uniform(90, 100, 1000),
            'close': np.random.uniform(100, 110, 1000),
            'volume': np.random.randint(1000, 10000, 1000)
        }, index=dates)
        
        # 数据预处理
        processed_df = data_processor.clean_data(df)
        processed_df = data_processor.calculate_technical_indicators(processed_df)
        processed_df = data_processor.calculate_price_features(processed_df)
        processed_df = data_processor.create_features_for_ml(processed_df)
        processed_df = data_processor.create_target_variable(processed_df)
        
        assert len(processed_df) > 0, "处理后应有数据"
        
        # 2. 模型训练（简化测试）
        try:
            result = model_manager.train_ml_model("TEST", processed_df, "random_forest")
            assert result is not None, "模型训练应成功"
        except Exception as e:
            # 模型训练可能因为数据不足等原因失败，这在测试中是可接受的
            print(f"模型训练跳过: {e}")
        
        # 3. 风险管理测试
        risk_manager.current_capital = 100000
        risk_manager.positions.clear()
        
        # 开仓测试
        success = risk_manager.open_position("TEST", 100, 150.0, "long")
        assert success, "开仓应成功"
        
        # 更新价格
        risk_manager.update_positions({"TEST": 155.0})
        
        # 获取风险指标
        metrics = risk_manager.get_risk_metrics()
        assert metrics.portfolio_value > 0, "组合价值应大于0"
        
        # 4. 交易执行测试
        execution_engine.set_live_trading(False)
        
        signal = TradingSignal(
            symbol="TEST",
            action="SELL",
            confidence=0.8,
            price=155.0
        )
        
        order = execution_engine.process_trading_signal(signal)
        # 在测试环境中，订单处理结果可能因为各种原因而不同
        
        print("端到端测试完成")


def run_tests():
    """运行所有测试"""
    print("🧪 开始运行系统测试...")
    
    # 运行pytest
    pytest.main([__file__, "-v"])


if __name__ == "__main__":
    run_tests()

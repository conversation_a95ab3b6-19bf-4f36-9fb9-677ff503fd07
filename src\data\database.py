"""
数据库管理模块 - 支持PostgreSQL和InfluxDB
"""
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from sqlalchemy import create_engine, Column, Integer, String, Float, DateTime, Boolean, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from influxdb_client import InfluxDBClient, Point, WritePrecision
from influxdb_client.client.write_api import SYNCHRONOUS
import redis
from loguru import logger

from config.settings import settings

# SQLAlchemy基类
Base = declarative_base()


class Stock(Base):
    """股票基本信息表"""
    __tablename__ = 'stocks'
    
    id = Column(Integer, primary_key=True)
    symbol = Column(String(10), unique=True, nullable=False)
    name = Column(String(100))
    exchange = Column(String(20))
    sector = Column(String(50))
    industry = Column(String(100))
    market_cap = Column(Float)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class TradingSignal(Base):
    """交易信号表"""
    __tablename__ = 'trading_signals'
    
    id = Column(Integer, primary_key=True)
    symbol = Column(String(10), nullable=False)
    signal_type = Column(String(20), nullable=False)  # BUY, SELL, HOLD
    confidence = Column(Float, nullable=False)
    price = Column(Float, nullable=False)
    model_name = Column(String(50))
    features = Column(Text)  # JSON格式的特征数据
    created_at = Column(DateTime, default=datetime.utcnow)


class Trade(Base):
    """交易记录表"""
    __tablename__ = 'trades'
    
    id = Column(Integer, primary_key=True)
    symbol = Column(String(10), nullable=False)
    side = Column(String(10), nullable=False)  # BUY, SELL
    quantity = Column(Float, nullable=False)
    price = Column(Float, nullable=False)
    order_id = Column(String(50))
    status = Column(String(20), default='PENDING')  # PENDING, FILLED, CANCELLED
    signal_id = Column(Integer)
    executed_at = Column(DateTime)
    created_at = Column(DateTime, default=datetime.utcnow)


class ModelPerformance(Base):
    """模型性能记录表"""
    __tablename__ = 'model_performance'
    
    id = Column(Integer, primary_key=True)
    model_name = Column(String(50), nullable=False)
    symbol = Column(String(10))
    accuracy = Column(Float)
    precision = Column(Float)
    recall = Column(Float)
    f1_score = Column(Float)
    sharpe_ratio = Column(Float)
    max_drawdown = Column(Float)
    total_return = Column(Float)
    evaluation_period_start = Column(DateTime)
    evaluation_period_end = Column(DateTime)
    created_at = Column(DateTime, default=datetime.utcnow)


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        # PostgreSQL连接
        self.engine = create_engine(settings.database_url)
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        
        # InfluxDB连接
        self.influx_client = InfluxDBClient(
            url=settings.INFLUXDB_URL,
            token=settings.INFLUXDB_TOKEN,
            org=settings.INFLUXDB_ORG
        )
        self.write_api = self.influx_client.write_api(write_options=SYNCHRONOUS)
        self.query_api = self.influx_client.query_api()
        
        # Redis连接
        self.redis_client = redis.from_url(settings.redis_url)
        
        # 创建表
        self.create_tables()
    
    def create_tables(self):
        """创建数据库表"""
        try:
            Base.metadata.create_all(bind=self.engine)
            logger.info("数据库表创建成功")
        except Exception as e:
            logger.error(f"数据库表创建失败: {e}")
    
    def get_session(self) -> Session:
        """获取数据库会话"""
        return self.SessionLocal()
    
    def close(self):
        """关闭数据库连接"""
        self.influx_client.close()
        self.redis_client.close()
    
    # InfluxDB操作方法
    def write_market_data(self, symbol: str, data: Dict[str, Any]):
        """写入市场数据到InfluxDB"""
        try:
            point = Point("market_data") \
                .tag("symbol", symbol) \
                .tag("source", data.get("source", "unknown"))
            
            # 添加字段
            if "price" in data:
                point = point.field("price", float(data["price"]))
            if "volume" in data:
                point = point.field("volume", float(data["volume"]))
            if "bid_price" in data:
                point = point.field("bid_price", float(data["bid_price"]))
            if "ask_price" in data:
                point = point.field("ask_price", float(data["ask_price"]))
            if "bid_size" in data:
                point = point.field("bid_size", float(data["bid_size"]))
            if "ask_size" in data:
                point = point.field("ask_size", float(data["ask_size"]))
            
            # 设置时间戳
            if "timestamp" in data:
                if isinstance(data["timestamp"], (int, float)):
                    timestamp = datetime.fromtimestamp(data["timestamp"] / 1000)
                else:
                    timestamp = data["timestamp"]
                point = point.time(timestamp, WritePrecision.MS)
            else:
                point = point.time(datetime.utcnow(), WritePrecision.MS)
            
            self.write_api.write(bucket=settings.INFLUXDB_BUCKET, record=point)
            
        except Exception as e:
            logger.error(f"写入市场数据失败: {e}")
    
    def write_ohlcv_data(self, symbol: str, df: pd.DataFrame):
        """批量写入OHLCV数据到InfluxDB"""
        try:
            points = []
            for timestamp, row in df.iterrows():
                point = Point("ohlcv") \
                    .tag("symbol", symbol) \
                    .field("open", float(row["open"])) \
                    .field("high", float(row["high"])) \
                    .field("low", float(row["low"])) \
                    .field("close", float(row["close"])) \
                    .field("volume", float(row["volume"])) \
                    .time(timestamp, WritePrecision.MS)
                
                # 添加技术指标（如果存在）
                for col in row.index:
                    if col not in ["open", "high", "low", "close", "volume"] and pd.notna(row[col]):
                        try:
                            point = point.field(col, float(row[col]))
                        except (ValueError, TypeError):
                            continue
                
                points.append(point)
            
            self.write_api.write(bucket=settings.INFLUXDB_BUCKET, record=points)
            logger.info(f"成功写入 {len(points)} 条OHLCV数据")
            
        except Exception as e:
            logger.error(f"批量写入OHLCV数据失败: {e}")
    
    def query_market_data(self, symbol: str, start_time: datetime, 
                         end_time: datetime, measurement: str = "ohlcv") -> pd.DataFrame:
        """查询市场数据"""
        try:
            query = f'''
            from(bucket: "{settings.INFLUXDB_BUCKET}")
                |> range(start: {start_time.isoformat()}Z, stop: {end_time.isoformat()}Z)
                |> filter(fn: (r) => r._measurement == "{measurement}")
                |> filter(fn: (r) => r.symbol == "{symbol}")
                |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
            '''
            
            result = self.query_api.query_data_frame(query)
            
            if not result.empty:
                result['_time'] = pd.to_datetime(result['_time'])
                result.set_index('_time', inplace=True)
                result.index.name = 'timestamp'
                
                # 删除不需要的列
                columns_to_drop = ['result', 'table', '_start', '_stop', '_measurement', 'symbol']
                result = result.drop(columns=[col for col in columns_to_drop if col in result.columns])
            
            return result
            
        except Exception as e:
            logger.error(f"查询市场数据失败: {e}")
            return pd.DataFrame()
    
    def get_latest_price(self, symbol: str) -> Optional[float]:
        """获取最新价格"""
        try:
            query = f'''
            from(bucket: "{settings.INFLUXDB_BUCKET}")
                |> range(start: -1h)
                |> filter(fn: (r) => r._measurement == "market_data")
                |> filter(fn: (r) => r.symbol == "{symbol}")
                |> filter(fn: (r) => r._field == "price")
                |> last()
            '''
            
            result = self.query_api.query_data_frame(query)
            
            if not result.empty:
                return float(result.iloc[0]['_value'])
            
            return None
            
        except Exception as e:
            logger.error(f"获取最新价格失败: {e}")
            return None
    
    # Redis缓存操作
    def cache_set(self, key: str, value: Any, expire: int = 3600):
        """设置缓存"""
        try:
            import json
            if isinstance(value, (dict, list)):
                value = json.dumps(value)
            self.redis_client.setex(key, expire, value)
        except Exception as e:
            logger.error(f"设置缓存失败: {e}")
    
    def cache_get(self, key: str) -> Optional[Any]:
        """获取缓存"""
        try:
            import json
            value = self.redis_client.get(key)
            if value:
                value = value.decode('utf-8')
                try:
                    return json.loads(value)
                except json.JSONDecodeError:
                    return value
            return None
        except Exception as e:
            logger.error(f"获取缓存失败: {e}")
            return None
    
    def cache_delete(self, key: str):
        """删除缓存"""
        try:
            self.redis_client.delete(key)
        except Exception as e:
            logger.error(f"删除缓存失败: {e}")
    
    # PostgreSQL操作方法
    def save_trading_signal(self, signal_data: Dict[str, Any]) -> int:
        """保存交易信号"""
        try:
            session = self.get_session()
            
            signal = TradingSignal(
                symbol=signal_data["symbol"],
                signal_type=signal_data["signal_type"],
                confidence=signal_data["confidence"],
                price=signal_data["price"],
                model_name=signal_data.get("model_name"),
                features=signal_data.get("features")
            )
            
            session.add(signal)
            session.commit()
            signal_id = signal.id
            session.close()
            
            return signal_id
            
        except Exception as e:
            logger.error(f"保存交易信号失败: {e}")
            return None
    
    def save_trade(self, trade_data: Dict[str, Any]) -> int:
        """保存交易记录"""
        try:
            session = self.get_session()
            
            trade = Trade(
                symbol=trade_data["symbol"],
                side=trade_data["side"],
                quantity=trade_data["quantity"],
                price=trade_data["price"],
                order_id=trade_data.get("order_id"),
                status=trade_data.get("status", "PENDING"),
                signal_id=trade_data.get("signal_id"),
                executed_at=trade_data.get("executed_at")
            )
            
            session.add(trade)
            session.commit()
            trade_id = trade.id
            session.close()
            
            return trade_id
            
        except Exception as e:
            logger.error(f"保存交易记录失败: {e}")
            return None
    
    def get_trading_signals(self, symbol: str = None, limit: int = 100) -> List[Dict]:
        """获取交易信号"""
        try:
            session = self.get_session()
            
            query = session.query(TradingSignal)
            if symbol:
                query = query.filter(TradingSignal.symbol == symbol)
            
            signals = query.order_by(TradingSignal.created_at.desc()).limit(limit).all()
            
            result = []
            for signal in signals:
                result.append({
                    "id": signal.id,
                    "symbol": signal.symbol,
                    "signal_type": signal.signal_type,
                    "confidence": signal.confidence,
                    "price": signal.price,
                    "model_name": signal.model_name,
                    "created_at": signal.created_at
                })
            
            session.close()
            return result
            
        except Exception as e:
            logger.error(f"获取交易信号失败: {e}")
            return []
    
    def get_trades(self, symbol: str = None, limit: int = 100) -> List[Dict]:
        """获取交易记录"""
        try:
            session = self.get_session()
            
            query = session.query(Trade)
            if symbol:
                query = query.filter(Trade.symbol == symbol)
            
            trades = query.order_by(Trade.created_at.desc()).limit(limit).all()
            
            result = []
            for trade in trades:
                result.append({
                    "id": trade.id,
                    "symbol": trade.symbol,
                    "side": trade.side,
                    "quantity": trade.quantity,
                    "price": trade.price,
                    "order_id": trade.order_id,
                    "status": trade.status,
                    "executed_at": trade.executed_at,
                    "created_at": trade.created_at
                })
            
            session.close()
            return result
            
        except Exception as e:
            logger.error(f"获取交易记录失败: {e}")
            return []


# 全局数据库管理器实例
db_manager = DatabaseManager()

"""
实时市场数据获取模块
支持多数据源：Polygon.io, Alpha Vantage, Yahoo Finance
"""
import asyncio
import json
import websocket
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime, timedelta
import pandas as pd
import yfinance as yf
from polygon import RESTClient
from alpha_vantage.timeseries import TimeSeries
import requests
from loguru import logger

from config.settings import settings


class MarketDataProvider:
    """市场数据提供者基类"""
    
    def __init__(self):
        self.is_connected = False
        self.callbacks: List[Callable] = []
    
    def add_callback(self, callback: Callable):
        """添加数据回调函数"""
        self.callbacks.append(callback)
    
    def notify_callbacks(self, data: Dict[str, Any]):
        """通知所有回调函数"""
        for callback in self.callbacks:
            try:
                callback(data)
            except Exception as e:
                logger.error(f"回调函数执行错误: {e}")


class PolygonDataProvider(MarketDataProvider):
    """Polygon.io数据提供者"""
    
    def __init__(self, api_key: str):
        super().__init__()
        self.api_key = api_key
        self.client = RESTClient(api_key)
        self.ws = None
        self.subscribed_symbols = set()
    
    async def connect_websocket(self):
        """连接WebSocket实时数据流"""
        try:
            ws_url = f"wss://socket.polygon.io/stocks"
            self.ws = websocket.WebSocketApp(
                ws_url,
                on_open=self._on_open,
                on_message=self._on_message,
                on_error=self._on_error,
                on_close=self._on_close
            )
            
            # 在新线程中运行WebSocket
            import threading
            ws_thread = threading.Thread(target=self.ws.run_forever)
            ws_thread.daemon = True
            ws_thread.start()
            
            self.is_connected = True
            logger.info("Polygon WebSocket连接成功")
            
        except Exception as e:
            logger.error(f"Polygon WebSocket连接失败: {e}")
            self.is_connected = False
    
    def _on_open(self, ws):
        """WebSocket连接打开"""
        # 认证
        auth_msg = {
            "action": "auth",
            "params": self.api_key
        }
        ws.send(json.dumps(auth_msg))
        logger.info("Polygon WebSocket认证消息已发送")
    
    def _on_message(self, ws, message):
        """处理WebSocket消息"""
        try:
            data = json.loads(message)
            
            if isinstance(data, list):
                for item in data:
                    self._process_message(item)
            else:
                self._process_message(data)
                
        except Exception as e:
            logger.error(f"处理Polygon消息错误: {e}")
    
    def _process_message(self, message: Dict):
        """处理单个消息"""
        if message.get("ev") == "T":  # Trade data
            trade_data = {
                "symbol": message.get("sym"),
                "price": message.get("p"),
                "size": message.get("s"),
                "timestamp": message.get("t"),
                "exchange": message.get("x"),
                "data_type": "trade",
                "source": "polygon"
            }
            self.notify_callbacks(trade_data)
            
        elif message.get("ev") == "Q":  # Quote data
            quote_data = {
                "symbol": message.get("sym"),
                "bid_price": message.get("bp"),
                "ask_price": message.get("ap"),
                "bid_size": message.get("bs"),
                "ask_size": message.get("as"),
                "timestamp": message.get("t"),
                "data_type": "quote",
                "source": "polygon"
            }
            self.notify_callbacks(quote_data)
    
    def _on_error(self, ws, error):
        """WebSocket错误处理"""
        logger.error(f"Polygon WebSocket错误: {error}")
        self.is_connected = False
    
    def _on_close(self, ws, close_status_code, close_msg):
        """WebSocket连接关闭"""
        logger.info("Polygon WebSocket连接已关闭")
        self.is_connected = False
    
    def subscribe_symbol(self, symbol: str):
        """订阅股票实时数据"""
        if self.ws and self.is_connected:
            subscribe_msg = {
                "action": "subscribe",
                "params": f"T.{symbol},Q.{symbol}"  # 订阅交易和报价数据
            }
            self.ws.send(json.dumps(subscribe_msg))
            self.subscribed_symbols.add(symbol)
            logger.info(f"已订阅 {symbol} 的实时数据")
    
    def get_historical_data(self, symbol: str, days: int = 30) -> pd.DataFrame:
        """获取历史数据"""
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            # 获取日线数据
            aggs = self.client.get_aggs(
                ticker=symbol,
                multiplier=1,
                timespan="day",
                from_=start_date.strftime("%Y-%m-%d"),
                to=end_date.strftime("%Y-%m-%d")
            )
            
            data = []
            for agg in aggs:
                data.append({
                    "timestamp": datetime.fromtimestamp(agg.timestamp / 1000),
                    "open": agg.open,
                    "high": agg.high,
                    "low": agg.low,
                    "close": agg.close,
                    "volume": agg.volume
                })
            
            df = pd.DataFrame(data)
            df.set_index("timestamp", inplace=True)
            return df
            
        except Exception as e:
            logger.error(f"获取 {symbol} 历史数据失败: {e}")
            return pd.DataFrame()


class AlphaVantageProvider(MarketDataProvider):
    """Alpha Vantage数据提供者"""
    
    def __init__(self, api_key: str):
        super().__init__()
        self.api_key = api_key
        self.ts = TimeSeries(key=api_key, output_format='pandas')
    
    def get_intraday_data(self, symbol: str, interval: str = "1min") -> pd.DataFrame:
        """获取分钟级数据"""
        try:
            data, meta_data = self.ts.get_intraday(
                symbol=symbol,
                interval=interval,
                outputsize='full'
            )
            
            # 重命名列
            data.columns = ['open', 'high', 'low', 'close', 'volume']
            data.index.name = 'timestamp'
            
            return data
            
        except Exception as e:
            logger.error(f"获取 {symbol} 分钟数据失败: {e}")
            return pd.DataFrame()
    
    def get_daily_data(self, symbol: str) -> pd.DataFrame:
        """获取日线数据"""
        try:
            data, meta_data = self.ts.get_daily_adjusted(symbol=symbol, outputsize='full')
            
            # 重命名列
            data.columns = ['open', 'high', 'low', 'close', 'adjusted_close', 'volume', 'dividend', 'split']
            data.index.name = 'timestamp'
            
            return data
            
        except Exception as e:
            logger.error(f"获取 {symbol} 日线数据失败: {e}")
            return pd.DataFrame()


class YahooFinanceProvider(MarketDataProvider):
    """Yahoo Finance数据提供者（备用数据源）"""
    
    def __init__(self):
        super().__init__()
    
    def get_realtime_price(self, symbol: str) -> Dict[str, Any]:
        """获取实时价格"""
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            
            return {
                "symbol": symbol,
                "price": info.get("regularMarketPrice"),
                "change": info.get("regularMarketChange"),
                "change_percent": info.get("regularMarketChangePercent"),
                "volume": info.get("regularMarketVolume"),
                "timestamp": datetime.now(),
                "source": "yahoo"
            }
            
        except Exception as e:
            logger.error(f"获取 {symbol} 实时价格失败: {e}")
            return {}
    
    def get_historical_data(self, symbol: str, period: str = "1mo") -> pd.DataFrame:
        """获取历史数据"""
        try:
            ticker = yf.Ticker(symbol)
            data = ticker.history(period=period)
            
            # 标准化列名
            data.columns = [col.lower() for col in data.columns]
            data.index.name = 'timestamp'
            
            return data
            
        except Exception as e:
            logger.error(f"获取 {symbol} 历史数据失败: {e}")
            return pd.DataFrame()


class MarketDataManager:
    """市场数据管理器 - 统一管理多个数据源"""
    
    def __init__(self):
        self.providers = {}
        self.callbacks = []
        
        # 初始化数据提供者
        if settings.POLYGON_API_KEY:
            self.providers['polygon'] = PolygonDataProvider(settings.POLYGON_API_KEY)
        
        if settings.ALPHA_VANTAGE_API_KEY:
            self.providers['alpha_vantage'] = AlphaVantageProvider(settings.ALPHA_VANTAGE_API_KEY)
        
        self.providers['yahoo'] = YahooFinanceProvider()
        
        # 为所有提供者添加回调
        for provider in self.providers.values():
            provider.add_callback(self._on_data_received)
    
    def _on_data_received(self, data: Dict[str, Any]):
        """数据接收回调"""
        # 添加接收时间戳
        data['received_at'] = datetime.now()
        
        # 通知所有注册的回调函数
        for callback in self.callbacks:
            try:
                callback(data)
            except Exception as e:
                logger.error(f"数据回调执行错误: {e}")
    
    def add_callback(self, callback: Callable):
        """添加数据回调函数"""
        self.callbacks.append(callback)
    
    async def start_realtime_data(self, symbols: List[str]):
        """启动实时数据流"""
        if 'polygon' in self.providers:
            polygon_provider = self.providers['polygon']
            await polygon_provider.connect_websocket()
            
            # 订阅所有股票
            for symbol in symbols:
                polygon_provider.subscribe_symbol(symbol)
    
    def get_historical_data(self, symbol: str, source: str = "polygon", **kwargs) -> pd.DataFrame:
        """获取历史数据"""
        if source in self.providers:
            return self.providers[source].get_historical_data(symbol, **kwargs)
        else:
            logger.warning(f"数据源 {source} 不可用，使用Yahoo Finance")
            return self.providers['yahoo'].get_historical_data(symbol, **kwargs)
    
    def get_realtime_price(self, symbol: str) -> Dict[str, Any]:
        """获取实时价格（使用Yahoo Finance作为快速获取方式）"""
        return self.providers['yahoo'].get_realtime_price(symbol)


# 全局市场数据管理器实例
market_data_manager = MarketDataManager()

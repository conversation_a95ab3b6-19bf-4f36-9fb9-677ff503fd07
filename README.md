# 美股短线交易AI系统

一个基于机器学习的美股短线交易自动化系统，集成了实时数据获取、AI预测模型、风险管理、交易执行和监控界面。

## 🚀 主要功能

### 核心功能
- **实时数据获取**: 支持Polygon.io、Alpha Vantage、Yahoo Finance等多数据源
- **AI预测模型**: LSTM神经网络 + 传统机器学习模型集成
- **智能风险管理**: 动态仓位管理、止损止盈、风险评估
- **自动交易执行**: 集成Alpaca券商API，支持实盘和模拟交易
- **实时监控界面**: Streamlit仪表板 + REST API
- **回测系统**: 历史数据验证和策略优化

### 技术特色
- **多模型集成**: LSTM + RandomForest + XGBoost + SVM
- **实时流处理**: WebSocket数据流 + 异步处理
- **微服务架构**: Docker容器化部署
- **时序数据库**: InfluxDB存储市场数据
- **监控告警**: Prometheus + Grafana

## 📋 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   数据获取层     │    │    AI模型层     │    │   交易执行层     │
│                │    │                │    │                │
│ • Polygon.io   │    │ • LSTM模型     │    │ • Alpaca API   │
│ • Alpha Vantage│────▶│ • 随机森林      │────▶│ • 订单管理      │
│ • Yahoo Finance│    │ • XGBoost      │    │ • 风险控制      │
│                │    │ • 模型集成      │    │                │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   数据存储层     │    │   策略引擎层     │    │   监控界面层     │
│                │    │                │    │                │
│ • PostgreSQL   │    │ • 策略管理      │    │ • Streamlit    │
│ • InfluxDB     │◀───│ • 信号生成      │    │ • REST API     │
│ • Redis缓存    │    │ • 回测分析      │    │ • Grafana      │
│                │    │                │    │                │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🛠️ 安装部署

### 环境要求
- Python 3.9+
- Docker & Docker Compose
- 8GB+ RAM
- 50GB+ 存储空间

### 快速开始

1. **克隆项目**
```bash
git clone <repository-url>
cd lianai
```

2. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，填入API密钥
```

3. **Docker部署**
```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f trading-ai
```

4. **初始化系统**
```bash
# 进入容器
docker exec -it trading-ai-app bash

# 初始化数据库
python main.py init

# 训练初始模型
python main.py train
```

### 本地开发

1. **安装依赖**
```bash
pip install -r requirements.txt
```

2. **启动数据库服务**
```bash
docker-compose up -d postgres redis influxdb
```

3. **运行应用**
```bash
# 启动API服务
python main.py api

# 启动Web仪表板
python main.py dashboard

# 启动交易策略
python main.py strategy
```

## 📊 使用指南

### 1. 配置API密钥

在 `.env` 文件中配置以下API密钥：

```env
# 数据源API
POLYGON_API_KEY=your_polygon_api_key
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key

# 券商API (Alpaca)
ALPACA_API_KEY=your_alpaca_key
ALPACA_SECRET_KEY=your_alpaca_secret
ALPACA_BASE_URL=https://paper-api.alpaca.markets  # 模拟交易

# 数据库配置
POSTGRES_PASSWORD=your_postgres_password
INFLUXDB_TOKEN=your_influxdb_token
```

### 2. 访问界面

- **Web仪表板**: http://localhost:8501
- **API文档**: http://localhost:8000/docs
- **Grafana监控**: http://localhost:3000 (admin/admin)
- **Prometheus**: http://localhost:9090

### 3. 基本操作

#### 训练模型
```bash
python main.py train
```

#### 运行回测
```bash
python main.py backtest
```

#### 启动实时交易
```bash
python main.py strategy
```

### 4. API使用示例

```python
import requests

# 获取策略状态
response = requests.get("http://localhost:8000/strategy/status")
print(response.json())

# 提交交易信号
signal = {
    "symbol": "AAPL",
    "action": "BUY", 
    "confidence": 0.85,
    "price": 150.0
}
response = requests.post("http://localhost:8000/trading/signal", json=signal)
print(response.json())
```

## 🔧 配置说明

### 风险管理参数

```python
# config/settings.py
MAX_POSITION_SIZE = 0.02        # 单笔交易最大仓位2%
MAX_DAILY_LOSS = 0.05          # 日最大损失5%
STOP_LOSS_PERCENT = 0.02       # 止损2%
TAKE_PROFIT_PERCENT = 0.04     # 止盈4%
```

### 模型参数

```python
# AI模型配置
MODEL_UPDATE_INTERVAL = 3600    # 模型更新间隔(秒)
PREDICTION_CONFIDENCE_THRESHOLD = 0.7  # 预测置信度阈值
```

### 支持的股票

默认支持以下股票交易：
- 个股: AAPL, MSFT, GOOGL, AMZN, TSLA, META, NVDA, NFLX
- ETF: SPY, QQQ, IWM, DIA, VIX, GLD, SLV, TLT

## 📈 性能指标

### 回测结果示例
- **年化收益率**: 15-25%
- **夏普比率**: 1.2-1.8
- **最大回撤**: <10%
- **胜率**: 55-65%

### 系统性能
- **预测延迟**: <100ms
- **订单执行**: <500ms
- **数据处理**: 1000+ ticks/秒
- **内存使用**: <4GB

## 🚨 风险提示

⚠️ **重要声明**：
1. 本系统仅供学习和研究使用
2. 实盘交易存在资金损失风险
3. 历史表现不代表未来收益
4. 请在充分理解风险的前提下使用
5. 建议先在模拟环境中测试

## 🔍 监控和维护

### 日志查看
```bash
# 查看应用日志
docker-compose logs -f trading-ai

# 查看特定服务日志
docker-compose logs -f postgres
```

### 数据备份
```bash
# 备份PostgreSQL
docker exec trading-ai-postgres pg_dump -U postgres trading_ai > backup.sql

# 备份InfluxDB
docker exec trading-ai-influxdb influx backup /backup
```

### 性能监控
- CPU/内存使用率
- 数据库连接数
- API响应时间
- 交易执行延迟

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

- 项目维护者: Trading AI Team
- 邮箱: <EMAIL>
- 文档: https://docs.trading-ai.com

## 🙏 致谢

感谢以下开源项目的支持：
- [TensorFlow](https://tensorflow.org/)
- [scikit-learn](https://scikit-learn.org/)
- [Streamlit](https://streamlit.io/)
- [FastAPI](https://fastapi.tiangolo.com/)
- [Alpaca Trade API](https://alpaca.markets/)

---

**免责声明**: 本软件仅供教育和研究目的使用。使用本软件进行实际交易的任何损失，开发者概不负责。投资有风险，入市需谨慎。

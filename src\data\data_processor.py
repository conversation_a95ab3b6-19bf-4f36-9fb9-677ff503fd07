"""
数据处理模块 - 负责数据清洗、特征工程和技术指标计算
"""
import pandas as pd
import numpy as np
import talib
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from loguru import logger

from config.settings import TECHNICAL_INDICATORS


class DataProcessor:
    """数据处理器"""
    
    def __init__(self):
        self.indicators_config = TECHNICAL_INDICATORS
    
    def clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """数据清洗"""
        try:
            # 复制数据避免修改原始数据
            cleaned_df = df.copy()
            
            # 删除重复行
            cleaned_df = cleaned_df.drop_duplicates()
            
            # 按时间排序
            cleaned_df = cleaned_df.sort_index()
            
            # 处理缺失值
            cleaned_df = cleaned_df.fillna(method='ffill')  # 前向填充
            cleaned_df = cleaned_df.fillna(method='bfill')  # 后向填充
            
            # 删除异常值（使用IQR方法）
            for column in ['open', 'high', 'low', 'close', 'volume']:
                if column in cleaned_df.columns:
                    Q1 = cleaned_df[column].quantile(0.25)
                    Q3 = cleaned_df[column].quantile(0.75)
                    IQR = Q3 - Q1
                    lower_bound = Q1 - 1.5 * IQR
                    upper_bound = Q3 + 1.5 * IQR
                    
                    # 将异常值替换为边界值
                    cleaned_df[column] = cleaned_df[column].clip(lower_bound, upper_bound)
            
            # 确保价格数据的逻辑性
            if all(col in cleaned_df.columns for col in ['open', 'high', 'low', 'close']):
                # 确保 high >= max(open, close) 和 low <= min(open, close)
                cleaned_df['high'] = np.maximum(cleaned_df['high'], 
                                              np.maximum(cleaned_df['open'], cleaned_df['close']))
                cleaned_df['low'] = np.minimum(cleaned_df['low'], 
                                             np.minimum(cleaned_df['open'], cleaned_df['close']))
            
            logger.info(f"数据清洗完成，处理了 {len(df)} 行数据")
            return cleaned_df
            
        except Exception as e:
            logger.error(f"数据清洗失败: {e}")
            return df
    
    def calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标"""
        try:
            result_df = df.copy()
            
            # 确保有必要的列
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            if not all(col in df.columns for col in required_columns):
                logger.error("缺少必要的OHLCV数据列")
                return result_df
            
            # 提取价格数据
            open_prices = df['open'].values
            high_prices = df['high'].values
            low_prices = df['low'].values
            close_prices = df['close'].values
            volume = df['volume'].values
            
            # 移动平均线
            for period in self.indicators_config['sma_periods']:
                result_df[f'SMA_{period}'] = talib.SMA(close_prices, timeperiod=period)
            
            # 指数移动平均线
            for period in self.indicators_config['ema_periods']:
                result_df[f'EMA_{period}'] = talib.EMA(close_prices, timeperiod=period)
            
            # RSI
            rsi_period = self.indicators_config['rsi_period']
            result_df['RSI'] = talib.RSI(close_prices, timeperiod=rsi_period)
            
            # MACD
            macd_fast = self.indicators_config['macd_fast']
            macd_slow = self.indicators_config['macd_slow']
            macd_signal = self.indicators_config['macd_signal']
            
            macd, macd_signal_line, macd_hist = talib.MACD(
                close_prices, 
                fastperiod=macd_fast, 
                slowperiod=macd_slow, 
                signalperiod=macd_signal
            )
            result_df['MACD'] = macd
            result_df['MACD_Signal'] = macd_signal_line
            result_df['MACD_Hist'] = macd_hist
            
            # 布林带
            bb_period = self.indicators_config['bollinger_period']
            bb_std = self.indicators_config['bollinger_std']
            
            bb_upper, bb_middle, bb_lower = talib.BBANDS(
                close_prices, 
                timeperiod=bb_period, 
                nbdevup=bb_std, 
                nbdevdn=bb_std
            )
            result_df['BB_Upper'] = bb_upper
            result_df['BB_Middle'] = bb_middle
            result_df['BB_Lower'] = bb_lower
            result_df['BB_Width'] = (bb_upper - bb_lower) / bb_middle
            result_df['BB_Position'] = (close_prices - bb_lower) / (bb_upper - bb_lower)
            
            # 随机指标
            slowk, slowd = talib.STOCH(high_prices, low_prices, close_prices)
            result_df['STOCH_K'] = slowk
            result_df['STOCH_D'] = slowd
            
            # 威廉指标
            result_df['WILLR'] = talib.WILLR(high_prices, low_prices, close_prices)
            
            # 平均真实波幅
            result_df['ATR'] = talib.ATR(high_prices, low_prices, close_prices)
            
            # 成交量指标
            result_df['OBV'] = talib.OBV(close_prices, volume)
            result_df['AD'] = talib.AD(high_prices, low_prices, close_prices, volume)
            
            # 价格变化率
            result_df['ROC'] = talib.ROC(close_prices, timeperiod=10)
            
            # 动量指标
            result_df['MOM'] = talib.MOM(close_prices, timeperiod=10)
            
            # 商品通道指数
            result_df['CCI'] = talib.CCI(high_prices, low_prices, close_prices)
            
            logger.info("技术指标计算完成")
            return result_df
            
        except Exception as e:
            logger.error(f"技术指标计算失败: {e}")
            return df
    
    def calculate_price_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算价格特征"""
        try:
            result_df = df.copy()
            
            # 价格变化
            result_df['price_change'] = result_df['close'].pct_change()
            result_df['price_change_abs'] = result_df['price_change'].abs()
            
            # 价格波动率（滚动标准差）
            result_df['volatility_5'] = result_df['price_change'].rolling(5).std()
            result_df['volatility_20'] = result_df['price_change'].rolling(20).std()
            
            # 高低价差
            result_df['high_low_ratio'] = result_df['high'] / result_df['low']
            result_df['high_low_diff'] = result_df['high'] - result_df['low']
            
            # 开盘收盘价差
            result_df['open_close_ratio'] = result_df['close'] / result_df['open']
            result_df['open_close_diff'] = result_df['close'] - result_df['open']
            
            # 成交量特征
            result_df['volume_change'] = result_df['volume'].pct_change()
            result_df['volume_ma_5'] = result_df['volume'].rolling(5).mean()
            result_df['volume_ma_20'] = result_df['volume'].rolling(20).mean()
            result_df['volume_ratio'] = result_df['volume'] / result_df['volume_ma_20']
            
            # 价格位置（在一定周期内的相对位置）
            for period in [5, 10, 20]:
                high_period = result_df['high'].rolling(period).max()
                low_period = result_df['low'].rolling(period).min()
                result_df[f'price_position_{period}'] = (
                    (result_df['close'] - low_period) / (high_period - low_period)
                )
            
            # 趋势特征
            result_df['trend_5'] = result_df['close'].rolling(5).apply(
                lambda x: 1 if x.iloc[-1] > x.iloc[0] else -1
            )
            result_df['trend_20'] = result_df['close'].rolling(20).apply(
                lambda x: 1 if x.iloc[-1] > x.iloc[0] else -1
            )
            
            logger.info("价格特征计算完成")
            return result_df
            
        except Exception as e:
            logger.error(f"价格特征计算失败: {e}")
            return df
    
    def create_features_for_ml(self, df: pd.DataFrame, lookback_periods: List[int] = [1, 2, 3, 5, 10]) -> pd.DataFrame:
        """为机器学习创建特征"""
        try:
            result_df = df.copy()
            
            # 滞后特征
            feature_columns = ['close', 'volume', 'RSI', 'MACD', 'BB_Position']
            
            for col in feature_columns:
                if col in result_df.columns:
                    for period in lookback_periods:
                        result_df[f'{col}_lag_{period}'] = result_df[col].shift(period)
            
            # 滚动统计特征
            for col in ['close', 'volume']:
                if col in result_df.columns:
                    for window in [5, 10, 20]:
                        result_df[f'{col}_mean_{window}'] = result_df[col].rolling(window).mean()
                        result_df[f'{col}_std_{window}'] = result_df[col].rolling(window).std()
                        result_df[f'{col}_min_{window}'] = result_df[col].rolling(window).min()
                        result_df[f'{col}_max_{window}'] = result_df[col].rolling(window).max()
            
            # 时间特征
            if isinstance(result_df.index, pd.DatetimeIndex):
                result_df['hour'] = result_df.index.hour
                result_df['day_of_week'] = result_df.index.dayofweek
                result_df['month'] = result_df.index.month
                result_df['quarter'] = result_df.index.quarter
            
            # 删除包含NaN的行
            result_df = result_df.dropna()
            
            logger.info(f"机器学习特征创建完成，特征数量: {len(result_df.columns)}")
            return result_df
            
        except Exception as e:
            logger.error(f"机器学习特征创建失败: {e}")
            return df
    
    def create_target_variable(self, df: pd.DataFrame, prediction_horizon: int = 1, 
                             threshold: float = 0.01) -> pd.DataFrame:
        """创建目标变量（用于监督学习）"""
        try:
            result_df = df.copy()
            
            # 计算未来收益率
            future_return = result_df['close'].shift(-prediction_horizon) / result_df['close'] - 1
            
            # 创建分类目标（上涨/下跌/横盘）
            result_df['target_return'] = future_return
            result_df['target_direction'] = np.where(
                future_return > threshold, 1,  # 上涨
                np.where(future_return < -threshold, -1, 0)  # 下跌 / 横盘
            )
            
            # 创建二分类目标（上涨/不上涨）
            result_df['target_binary'] = (future_return > threshold).astype(int)
            
            # 删除最后几行（没有未来数据）
            result_df = result_df.iloc[:-prediction_horizon]
            
            logger.info("目标变量创建完成")
            return result_df
            
        except Exception as e:
            logger.error(f"目标变量创建失败: {e}")
            return df
    
    def process_realtime_data(self, data: Dict) -> Dict:
        """处理实时数据"""
        try:
            processed_data = data.copy()
            
            # 添加处理时间戳
            processed_data['processed_at'] = datetime.now()
            
            # 数据验证
            if 'price' in processed_data:
                price = processed_data['price']
                if price <= 0:
                    logger.warning(f"异常价格数据: {price}")
                    return None
            
            # 计算简单的实时指标
            if 'price' in processed_data and hasattr(self, 'price_history'):
                self.price_history.append(processed_data['price'])
                
                # 保持最近100个价格点
                if len(self.price_history) > 100:
                    self.price_history.pop(0)
                
                # 计算简单移动平均
                if len(self.price_history) >= 5:
                    processed_data['sma_5'] = np.mean(self.price_history[-5:])
                if len(self.price_history) >= 20:
                    processed_data['sma_20'] = np.mean(self.price_history[-20:])
            else:
                self.price_history = []
            
            return processed_data
            
        except Exception as e:
            logger.error(f"实时数据处理失败: {e}")
            return data


# 全局数据处理器实例
data_processor = DataProcessor()

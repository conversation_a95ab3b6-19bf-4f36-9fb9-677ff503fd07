version: '3.8'

services:
  # 主应用服务
  trading-ai:
    build: .
    container_name: trading-ai-app
    ports:
      - "8000:8000"  # API服务
      - "8501:8501"  # Streamlit仪表板
    environment:
      - DEBUG=False
      - POSTGRES_HOST=postgres
      - REDIS_HOST=redis
      - INFLUXDB_URL=http://influxdb:8086
    env_file:
      - .env
    depends_on:
      - postgres
      - redis
      - influxdb
    volumes:
      - ./logs:/app/logs
      - ./models:/app/models
      - ./data:/app/data
    restart: unless-stopped
    networks:
      - trading-network

  # PostgreSQL数据库
  postgres:
    image: postgres:13
    container_name: trading-ai-postgres
    environment:
      - POSTGRES_DB=trading_ai
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped
    networks:
      - trading-network

  # Redis缓存
  redis:
    image: redis:6-alpine
    container_name: trading-ai-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - trading-network

  # InfluxDB时序数据库
  influxdb:
    image: influxdb:2.0
    container_name: trading-ai-influxdb
    ports:
      - "8086:8086"
    environment:
      - INFLUXDB_DB=market_data
      - INFLUXDB_ADMIN_USER=admin
      - INFLUXDB_ADMIN_PASSWORD=${INFLUXDB_ADMIN_PASSWORD}
      - INFLUXDB_USER=trading
      - INFLUXDB_USER_PASSWORD=${INFLUXDB_USER_PASSWORD}
    volumes:
      - influxdb_data:/var/lib/influxdb2
    restart: unless-stopped
    networks:
      - trading-network

  # Grafana监控面板
  grafana:
    image: grafana/grafana:latest
    container_name: trading-ai-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - influxdb
    restart: unless-stopped
    networks:
      - trading-network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: trading-ai-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - trading-ai
      - grafana
    restart: unless-stopped
    networks:
      - trading-network

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: trading-ai-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - trading-network

volumes:
  postgres_data:
  redis_data:
  influxdb_data:
  grafana_data:
  prometheus_data:

networks:
  trading-network:
    driver: bridge

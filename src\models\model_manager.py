"""
模型管理器 - 统一管理LSTM和传统ML模型
"""
import os
import json
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from loguru import logger

from .lstm_model import LSTMPredictor, LSTMEnsemble
from .ml_models import MLPredictor, MLEnsemble
from src.data.data_processor import data_processor
from src.data.database import db_manager
from config.settings import settings


class ModelManager:
    """模型管理器"""
    
    def __init__(self):
        self.models = {}
        self.model_performances = {}
        self.active_models = {}
        self.model_dir = "models"
        
        # 创建模型目录
        os.makedirs(self.model_dir, exist_ok=True)
    
    def train_lstm_model(self, symbol: str, df: pd.DataFrame, 
                        sequence_length: int = 60, test_size: float = 0.2) -> Dict[str, Any]:
        """训练LSTM模型"""
        try:
            logger.info(f"开始训练 {symbol} 的LSTM模型")
            
            # 数据预处理
            processed_df = data_processor.clean_data(df)
            processed_df = data_processor.calculate_technical_indicators(processed_df)
            processed_df = data_processor.calculate_price_features(processed_df)
            processed_df = data_processor.create_features_for_ml(processed_df)
            processed_df = data_processor.create_target_variable(processed_df)
            
            # 删除NaN行
            processed_df = processed_df.dropna()
            
            if len(processed_df) < sequence_length * 2:
                raise ValueError(f"数据量不足，需要至少 {sequence_length * 2} 行数据")
            
            # 创建LSTM模型
            lstm_model = LSTMPredictor(sequence_length=sequence_length)
            
            # 准备数据
            X, y = lstm_model.prepare_data(processed_df)
            
            # 分割训练和测试集
            split_idx = int(len(X) * (1 - test_size))
            X_train, X_test = X[:split_idx], X[split_idx:]
            y_train, y_test = y[:split_idx], y[split_idx:]
            
            # 进一步分割验证集
            val_split_idx = int(len(X_train) * 0.8)
            X_train_final, X_val = X_train[:val_split_idx], X_train[val_split_idx:]
            y_train_final, y_val = y_train[:val_split_idx], y_train[val_split_idx:]
            
            # 训练模型
            training_results = lstm_model.train(X_train_final, y_train_final, X_val, y_val)
            
            # 评估模型
            evaluation_results = lstm_model.evaluate(X_test, y_test)
            
            # 保存模型
            model_path = os.path.join(self.model_dir, f"{symbol}_lstm.h5")
            lstm_model.save_model(model_path)
            
            # 存储模型信息
            model_key = f"{symbol}_lstm"
            self.models[model_key] = {
                'model': lstm_model,
                'type': 'lstm',
                'symbol': symbol,
                'trained_at': datetime.now(),
                'model_path': model_path
            }
            
            # 存储性能信息
            self.model_performances[model_key] = {
                **evaluation_results,
                'training_samples': len(X_train_final),
                'test_samples': len(X_test),
                'sequence_length': sequence_length
            }
            
            # 保存性能到数据库
            self._save_performance_to_db(model_key, evaluation_results, symbol)
            
            results = {
                'model_key': model_key,
                'training_results': training_results,
                'evaluation_results': evaluation_results,
                'model_path': model_path
            }
            
            logger.info(f"{symbol} LSTM模型训练完成，准确率: {evaluation_results['accuracy']:.4f}")
            return results
            
        except Exception as e:
            logger.error(f"LSTM模型训练失败: {e}")
            raise
    
    def train_ml_model(self, symbol: str, df: pd.DataFrame, 
                      model_type: str = "random_forest", test_size: float = 0.2) -> Dict[str, Any]:
        """训练传统ML模型"""
        try:
            logger.info(f"开始训练 {symbol} 的{model_type}模型")
            
            # 数据预处理
            processed_df = data_processor.clean_data(df)
            processed_df = data_processor.calculate_technical_indicators(processed_df)
            processed_df = data_processor.calculate_price_features(processed_df)
            processed_df = data_processor.create_features_for_ml(processed_df)
            processed_df = data_processor.create_target_variable(processed_df)
            
            # 删除NaN行
            processed_df = processed_df.dropna()
            
            if len(processed_df) < 100:
                raise ValueError("数据量不足，需要至少100行数据")
            
            # 创建ML模型
            ml_model = MLPredictor(model_type=model_type)
            
            # 准备数据
            X, y = ml_model.prepare_features(processed_df)
            
            # 分割训练和测试集
            split_idx = int(len(X) * (1 - test_size))
            X_train, X_test = X[:split_idx], X[split_idx:]
            y_train, y_test = y[:split_idx], y[split_idx:]
            
            # 训练模型
            training_results = ml_model.train(X_train, y_train, optimize_hyperparameters=True)
            
            # 评估模型
            evaluation_results = ml_model.evaluate(X_test, y_test)
            
            # 保存模型
            model_path = os.path.join(self.model_dir, f"{symbol}_{model_type}.pkl")
            ml_model.save_model(model_path)
            
            # 存储模型信息
            model_key = f"{symbol}_{model_type}"
            self.models[model_key] = {
                'model': ml_model,
                'type': 'ml',
                'symbol': symbol,
                'model_type': model_type,
                'trained_at': datetime.now(),
                'model_path': model_path
            }
            
            # 存储性能信息
            self.model_performances[model_key] = {
                **evaluation_results,
                'training_samples': len(X_train),
                'test_samples': len(X_test),
                'feature_count': X.shape[1]
            }
            
            # 保存性能到数据库
            self._save_performance_to_db(model_key, evaluation_results, symbol)
            
            results = {
                'model_key': model_key,
                'training_results': training_results,
                'evaluation_results': evaluation_results,
                'feature_importance': ml_model.get_feature_importance(),
                'model_path': model_path
            }
            
            logger.info(f"{symbol} {model_type}模型训练完成，准确率: {evaluation_results['accuracy']:.4f}")
            return results
            
        except Exception as e:
            logger.error(f"{model_type}模型训练失败: {e}")
            raise
    
    def train_ensemble_models(self, symbol: str, df: pd.DataFrame) -> Dict[str, Any]:
        """训练集成模型"""
        try:
            logger.info(f"开始训练 {symbol} 的集成模型")
            
            results = {}
            
            # 训练LSTM集成
            try:
                lstm_ensemble = LSTMEnsemble(n_models=3)
                # 这里需要实现LSTM集成的训练逻辑
                results['lstm_ensemble'] = "LSTM集成训练完成"
            except Exception as e:
                logger.error(f"LSTM集成训练失败: {e}")
            
            # 训练ML集成
            try:
                processed_df = data_processor.clean_data(df)
                processed_df = data_processor.calculate_technical_indicators(processed_df)
                processed_df = data_processor.calculate_price_features(processed_df)
                processed_df = data_processor.create_features_for_ml(processed_df)
                processed_df = data_processor.create_target_variable(processed_df)
                processed_df = processed_df.dropna()
                
                ml_ensemble = MLEnsemble()
                
                # 准备数据
                X, y = ml_ensemble.models[list(ml_ensemble.model_types)[0]].prepare_features(processed_df) if ml_ensemble.model_types else (None, None)
                
                if X is not None:
                    # 分割数据
                    split_idx = int(len(X) * 0.8)
                    X_train, X_val = X[:split_idx], X[split_idx:]
                    y_train, y_val = y[:split_idx], y[split_idx:]
                    
                    # 训练集成模型
                    ensemble_results = ml_ensemble.train(X_train, y_train, X_val, y_val)
                    results['ml_ensemble'] = ensemble_results
                    
                    # 保存集成模型
                    ensemble_path = os.path.join(self.model_dir, f"{symbol}_ml_ensemble.pkl")
                    # 这里需要实现集成模型的保存逻辑
                    
            except Exception as e:
                logger.error(f"ML集成训练失败: {e}")
            
            return results
            
        except Exception as e:
            logger.error(f"集成模型训练失败: {e}")
            raise
    
    def predict(self, symbol: str, features: np.ndarray, model_types: List[str] = None) -> Dict[str, Any]:
        """使用指定模型进行预测"""
        try:
            if model_types is None:
                model_types = ['lstm', 'random_forest', 'xgboost']
            
            predictions = {}
            
            for model_type in model_types:
                model_key = f"{symbol}_{model_type}"
                
                if model_key in self.models:
                    model_info = self.models[model_key]
                    model = model_info['model']
                    
                    try:
                        if model_info['type'] == 'lstm':
                            # LSTM预测需要序列数据
                            if features.ndim == 2 and features.shape[0] >= model.sequence_length:
                                pred, conf = model.predict_single(features[-model.sequence_length:])
                                predictions[model_type] = {
                                    'prediction': int(pred),
                                    'confidence': float(conf),
                                    'model_type': 'lstm'
                                }
                        else:
                            # ML模型预测
                            if features.ndim == 1:
                                pred, conf = model.predict_single(features)
                                predictions[model_type] = {
                                    'prediction': int(pred),
                                    'confidence': float(conf),
                                    'model_type': 'ml'
                                }
                    except Exception as e:
                        logger.error(f"{model_key} 预测失败: {e}")
            
            # 集成预测
            if len(predictions) > 1:
                ensemble_prediction = self._ensemble_predict(predictions)
                predictions['ensemble'] = ensemble_prediction
            
            return predictions
            
        except Exception as e:
            logger.error(f"预测失败: {e}")
            return {}
    
    def _ensemble_predict(self, predictions: Dict[str, Any]) -> Dict[str, Any]:
        """集成预测"""
        try:
            # 简单的投票机制
            votes = {}
            total_confidence = 0
            count = 0
            
            for model_type, pred_info in predictions.items():
                if model_type != 'ensemble':
                    pred = pred_info['prediction']
                    conf = pred_info['confidence']
                    
                    if pred not in votes:
                        votes[pred] = 0
                    votes[pred] += conf
                    total_confidence += conf
                    count += 1
            
            if votes:
                final_prediction = max(votes.keys(), key=lambda k: votes[k])
                final_confidence = total_confidence / count
                
                return {
                    'prediction': final_prediction,
                    'confidence': final_confidence,
                    'model_type': 'ensemble',
                    'individual_predictions': predictions
                }
            
            return {}
            
        except Exception as e:
            logger.error(f"集成预测失败: {e}")
            return {}
    
    def _save_performance_to_db(self, model_name: str, performance: Dict[str, float], symbol: str):
        """保存模型性能到数据库"""
        try:
            session = db_manager.get_session()
            
            from src.data.database import ModelPerformance
            
            model_perf = ModelPerformance(
                model_name=model_name,
                symbol=symbol,
                accuracy=performance.get('accuracy'),
                precision=performance.get('precision'),
                recall=performance.get('recall'),
                f1_score=performance.get('f1_score'),
                evaluation_period_start=datetime.now() - timedelta(days=30),
                evaluation_period_end=datetime.now()
            )
            
            session.add(model_perf)
            session.commit()
            session.close()
            
        except Exception as e:
            logger.error(f"保存模型性能失败: {e}")
    
    def load_model(self, model_key: str) -> bool:
        """加载已保存的模型"""
        try:
            if model_key in self.models:
                model_info = self.models[model_key]
                model_path = model_info['model_path']
                
                if os.path.exists(model_path):
                    if model_info['type'] == 'lstm':
                        model = LSTMPredictor()
                        model.load_model(model_path)
                    else:
                        model = MLPredictor(model_type=model_info['model_type'])
                        model.load_model(model_path)
                    
                    self.models[model_key]['model'] = model
                    logger.info(f"模型 {model_key} 加载成功")
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            return False
    
    def get_model_performance(self, model_key: str) -> Dict[str, Any]:
        """获取模型性能"""
        return self.model_performances.get(model_key, {})
    
    def list_models(self) -> Dict[str, Any]:
        """列出所有模型"""
        model_list = {}
        for key, info in self.models.items():
            model_list[key] = {
                'symbol': info['symbol'],
                'type': info['type'],
                'trained_at': info['trained_at'],
                'performance': self.model_performances.get(key, {})
            }
        return model_list
    
    def set_active_model(self, symbol: str, model_key: str):
        """设置活跃模型"""
        if model_key in self.models:
            self.active_models[symbol] = model_key
            logger.info(f"设置 {symbol} 的活跃模型为 {model_key}")
    
    def get_active_model(self, symbol: str) -> Optional[str]:
        """获取活跃模型"""
        return self.active_models.get(symbol)


# 全局模型管理器实例
model_manager = ModelManager()

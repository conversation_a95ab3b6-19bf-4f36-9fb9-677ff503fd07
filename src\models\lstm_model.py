"""
LSTM神经网络模型 - 用于时序预测
"""
import numpy as np
import pandas as pd
from typing import Tuple, List, Dict, Any, Optional
from datetime import datetime
import tensorflow as tf
from tensorflow.keras.models import Sequential, Model
from tensorflow.keras.layers import LSTM, Dense, Dropout, BatchNormalization, Input, Attention
from tensorflow.keras.optimizers import <PERSON>
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau, ModelCheckpoint
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
import joblib
from loguru import logger


class LSTMPredictor:
    """LSTM预测模型"""
    
    def __init__(self, sequence_length: int = 60, features: List[str] = None):
        self.sequence_length = sequence_length
        self.features = features or ['close', 'volume', 'RSI', 'MACD', 'BB_Position']
        self.model = None
        self.scaler = StandardScaler()
        self.is_trained = False
        self.model_path = None
        
    def prepare_data(self, df: pd.DataFrame, target_column: str = 'target_direction') -> Tuple[np.ndarray, np.ndarray]:
        """准备训练数据"""
        try:
            # 选择特征列
            feature_columns = [col for col in self.features if col in df.columns]
            if not feature_columns:
                raise ValueError("没有找到有效的特征列")
            
            # 提取特征和目标
            X_data = df[feature_columns].values
            y_data = df[target_column].values
            
            # 数据标准化
            X_scaled = self.scaler.fit_transform(X_data)
            
            # 创建序列数据
            X_sequences = []
            y_sequences = []
            
            for i in range(self.sequence_length, len(X_scaled)):
                X_sequences.append(X_scaled[i-self.sequence_length:i])
                y_sequences.append(y_data[i])
            
            X_sequences = np.array(X_sequences)
            y_sequences = np.array(y_sequences)
            
            # 转换为分类标签（-1, 0, 1 -> 0, 1, 2）
            y_sequences = y_sequences + 1
            
            logger.info(f"数据准备完成: X shape: {X_sequences.shape}, y shape: {y_sequences.shape}")
            return X_sequences, y_sequences
            
        except Exception as e:
            logger.error(f"数据准备失败: {e}")
            raise
    
    def build_model(self, input_shape: Tuple[int, int], num_classes: int = 3) -> Model:
        """构建LSTM模型"""
        try:
            model = Sequential([
                # 第一层LSTM
                LSTM(128, return_sequences=True, input_shape=input_shape),
                Dropout(0.2),
                BatchNormalization(),
                
                # 第二层LSTM
                LSTM(64, return_sequences=True),
                Dropout(0.2),
                BatchNormalization(),
                
                # 第三层LSTM
                LSTM(32, return_sequences=False),
                Dropout(0.2),
                BatchNormalization(),
                
                # 全连接层
                Dense(50, activation='relu'),
                Dropout(0.3),
                
                Dense(25, activation='relu'),
                Dropout(0.2),
                
                # 输出层
                Dense(num_classes, activation='softmax')
            ])
            
            # 编译模型
            model.compile(
                optimizer=Adam(learning_rate=0.001),
                loss='sparse_categorical_crossentropy',
                metrics=['accuracy']
            )
            
            logger.info("LSTM模型构建完成")
            return model
            
        except Exception as e:
            logger.error(f"模型构建失败: {e}")
            raise
    
    def train(self, X_train: np.ndarray, y_train: np.ndarray, 
              X_val: np.ndarray = None, y_val: np.ndarray = None,
              epochs: int = 100, batch_size: int = 32) -> Dict[str, Any]:
        """训练模型"""
        try:
            # 构建模型
            input_shape = (X_train.shape[1], X_train.shape[2])
            self.model = self.build_model(input_shape)
            
            # 设置回调函数
            callbacks = [
                EarlyStopping(
                    monitor='val_loss' if X_val is not None else 'loss',
                    patience=15,
                    restore_best_weights=True
                ),
                ReduceLROnPlateau(
                    monitor='val_loss' if X_val is not None else 'loss',
                    factor=0.5,
                    patience=10,
                    min_lr=1e-7
                )
            ]
            
            # 如果有模型保存路径，添加模型检查点
            if self.model_path:
                callbacks.append(
                    ModelCheckpoint(
                        self.model_path,
                        monitor='val_loss' if X_val is not None else 'loss',
                        save_best_only=True,
                        save_weights_only=False
                    )
                )
            
            # 训练模型
            validation_data = (X_val, y_val) if X_val is not None and y_val is not None else None
            
            history = self.model.fit(
                X_train, y_train,
                epochs=epochs,
                batch_size=batch_size,
                validation_data=validation_data,
                callbacks=callbacks,
                verbose=1
            )
            
            self.is_trained = True
            
            # 返回训练历史
            training_results = {
                'history': history.history,
                'final_loss': history.history['loss'][-1],
                'final_accuracy': history.history['accuracy'][-1]
            }
            
            if validation_data:
                training_results['final_val_loss'] = history.history['val_loss'][-1]
                training_results['final_val_accuracy'] = history.history['val_accuracy'][-1]
            
            logger.info("模型训练完成")
            return training_results
            
        except Exception as e:
            logger.error(f"模型训练失败: {e}")
            raise
    
    def predict(self, X: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """预测"""
        try:
            if not self.is_trained or self.model is None:
                raise ValueError("模型尚未训练")
            
            # 标准化输入数据
            if len(X.shape) == 2:
                # 单个样本预测
                X_scaled = self.scaler.transform(X)
                X_scaled = X_scaled.reshape(1, X_scaled.shape[0], X_scaled.shape[1])
            else:
                # 批量预测
                original_shape = X.shape
                X_reshaped = X.reshape(-1, X.shape[-1])
                X_scaled = self.scaler.transform(X_reshaped)
                X_scaled = X_scaled.reshape(original_shape)
            
            # 预测
            predictions = self.model.predict(X_scaled)
            predicted_classes = np.argmax(predictions, axis=1)
            confidence_scores = np.max(predictions, axis=1)
            
            # 转换回原始标签（0, 1, 2 -> -1, 0, 1）
            predicted_classes = predicted_classes - 1
            
            return predicted_classes, confidence_scores
            
        except Exception as e:
            logger.error(f"预测失败: {e}")
            raise
    
    def predict_single(self, sequence: np.ndarray) -> Tuple[int, float]:
        """单个序列预测"""
        try:
            if sequence.shape[0] != self.sequence_length:
                raise ValueError(f"序列长度必须为 {self.sequence_length}")
            
            # 重塑为模型输入格式
            X = sequence.reshape(1, sequence.shape[0], sequence.shape[1])
            
            predictions, confidence = self.predict(X)
            
            return int(predictions[0]), float(confidence[0])
            
        except Exception as e:
            logger.error(f"单个预测失败: {e}")
            raise
    
    def evaluate(self, X_test: np.ndarray, y_test: np.ndarray) -> Dict[str, float]:
        """评估模型性能"""
        try:
            if not self.is_trained or self.model is None:
                raise ValueError("模型尚未训练")
            
            # 预测
            predictions, confidence = self.predict(X_test)
            
            # 转换测试标签
            y_test_converted = y_test - 1
            
            # 计算指标
            accuracy = accuracy_score(y_test_converted, predictions)
            precision = precision_score(y_test_converted, predictions, average='weighted', zero_division=0)
            recall = recall_score(y_test_converted, predictions, average='weighted', zero_division=0)
            f1 = f1_score(y_test_converted, predictions, average='weighted', zero_division=0)
            
            # 计算每个类别的准确率
            class_accuracies = {}
            for class_label in [-1, 0, 1]:
                mask = y_test_converted == class_label
                if np.sum(mask) > 0:
                    class_acc = accuracy_score(y_test_converted[mask], predictions[mask])
                    class_accuracies[f'accuracy_class_{class_label}'] = class_acc
            
            results = {
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1_score': f1,
                'mean_confidence': np.mean(confidence),
                **class_accuracies
            }
            
            logger.info(f"模型评估完成: {results}")
            return results
            
        except Exception as e:
            logger.error(f"模型评估失败: {e}")
            raise
    
    def save_model(self, filepath: str):
        """保存模型"""
        try:
            if self.model is None:
                raise ValueError("没有可保存的模型")
            
            # 保存模型
            self.model.save(filepath)
            
            # 保存标准化器
            scaler_path = filepath.replace('.h5', '_scaler.pkl')
            joblib.dump(self.scaler, scaler_path)
            
            # 保存配置
            config = {
                'sequence_length': self.sequence_length,
                'features': self.features,
                'is_trained': self.is_trained
            }
            config_path = filepath.replace('.h5', '_config.pkl')
            joblib.dump(config, config_path)
            
            logger.info(f"模型已保存到: {filepath}")
            
        except Exception as e:
            logger.error(f"模型保存失败: {e}")
            raise
    
    def load_model(self, filepath: str):
        """加载模型"""
        try:
            # 加载模型
            self.model = tf.keras.models.load_model(filepath)
            
            # 加载标准化器
            scaler_path = filepath.replace('.h5', '_scaler.pkl')
            self.scaler = joblib.load(scaler_path)
            
            # 加载配置
            config_path = filepath.replace('.h5', '_config.pkl')
            config = joblib.load(config_path)
            
            self.sequence_length = config['sequence_length']
            self.features = config['features']
            self.is_trained = config['is_trained']
            
            logger.info(f"模型已从 {filepath} 加载")
            
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise
    
    def get_feature_importance(self) -> Dict[str, float]:
        """获取特征重要性（简化版本）"""
        try:
            if not self.is_trained or self.model is None:
                raise ValueError("模型尚未训练")
            
            # 这是一个简化的特征重要性计算
            # 在实际应用中，可以使用更复杂的方法如SHAP
            weights = self.model.layers[0].get_weights()[0]  # 第一层的权重
            feature_importance = np.mean(np.abs(weights), axis=(0, 1))
            
            importance_dict = {}
            for i, feature in enumerate(self.features):
                if i < len(feature_importance):
                    importance_dict[feature] = float(feature_importance[i])
            
            return importance_dict
            
        except Exception as e:
            logger.error(f"获取特征重要性失败: {e}")
            return {}


class LSTMEnsemble:
    """LSTM集成模型"""
    
    def __init__(self, n_models: int = 3, sequence_length: int = 60):
        self.n_models = n_models
        self.sequence_length = sequence_length
        self.models = []
        self.is_trained = False
    
    def train(self, X_train: np.ndarray, y_train: np.ndarray, 
              X_val: np.ndarray = None, y_val: np.ndarray = None) -> List[Dict[str, Any]]:
        """训练集成模型"""
        try:
            results = []
            
            for i in range(self.n_models):
                logger.info(f"训练第 {i+1}/{self.n_models} 个模型")
                
                # 创建模型
                model = LSTMPredictor(sequence_length=self.sequence_length)
                
                # 训练模型
                result = model.train(X_train, y_train, X_val, y_val)
                
                self.models.append(model)
                results.append(result)
            
            self.is_trained = True
            logger.info("集成模型训练完成")
            return results
            
        except Exception as e:
            logger.error(f"集成模型训练失败: {e}")
            raise
    
    def predict(self, X: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """集成预测"""
        try:
            if not self.is_trained:
                raise ValueError("集成模型尚未训练")
            
            all_predictions = []
            all_confidences = []
            
            for model in self.models:
                pred, conf = model.predict(X)
                all_predictions.append(pred)
                all_confidences.append(conf)
            
            # 投票决策
            all_predictions = np.array(all_predictions)
            final_predictions = []
            final_confidences = []
            
            for i in range(X.shape[0]):
                # 多数投票
                votes = all_predictions[:, i]
                unique, counts = np.unique(votes, return_counts=True)
                final_pred = unique[np.argmax(counts)]
                
                # 平均置信度
                final_conf = np.mean([all_confidences[j][i] for j in range(len(self.models))])
                
                final_predictions.append(final_pred)
                final_confidences.append(final_conf)
            
            return np.array(final_predictions), np.array(final_confidences)
            
        except Exception as e:
            logger.error(f"集成预测失败: {e}")
            raise

    def evaluate(self, X_test: np.ndarray, y_test: np.ndarray) -> Dict[str, float]:
        """评估集成模型"""
        try:
            predictions, confidence = self.predict(X_test)

            # 转换测试标签
            y_test_converted = y_test - 1

            # 计算指标
            accuracy = accuracy_score(y_test_converted, predictions)
            precision = precision_score(y_test_converted, predictions, average='weighted', zero_division=0)
            recall = recall_score(y_test_converted, predictions, average='weighted', zero_division=0)
            f1 = f1_score(y_test_converted, predictions, average='weighted', zero_division=0)

            results = {
                'ensemble_accuracy': accuracy,
                'ensemble_precision': precision,
                'ensemble_recall': recall,
                'ensemble_f1_score': f1,
                'ensemble_mean_confidence': np.mean(confidence)
            }

            logger.info(f"集成模型评估完成: {results}")
            return results

        except Exception as e:
            logger.error(f"集成模型评估失败: {e}")
            raise

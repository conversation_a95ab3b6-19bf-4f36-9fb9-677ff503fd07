"""
Streamlit仪表板 - 实时监控和管理界面
"""
import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from datetime import datetime, timedelta
import asyncio
import threading
import time
from loguru import logger

from src.trading.strategy_engine import strategy_engine
from src.trading.risk_manager import risk_manager
from src.trading.execution_engine import execution_engine
from src.models.model_manager import model_manager
from src.data.database import db_manager
from src.backtesting.backtest_engine import backtest_engine
from config.settings import SUPPORTED_SYMBOLS


class TradingDashboard:
    """交易仪表板"""
    
    def __init__(self):
        self.setup_page()
        
    def setup_page(self):
        """设置页面配置"""
        st.set_page_config(
            page_title="美股短线交易AI系统",
            page_icon="📈",
            layout="wide",
            initial_sidebar_state="expanded"
        )
        
        # 自定义CSS
        st.markdown("""
        <style>
        .metric-card {
            background-color: #f0f2f6;
            padding: 1rem;
            border-radius: 0.5rem;
            border-left: 4px solid #1f77b4;
        }
        .profit {
            color: #00ff00;
        }
        .loss {
            color: #ff0000;
        }
        </style>
        """, unsafe_allow_html=True)
    
    def run(self):
        """运行仪表板"""
        st.title("🤖 美股短线交易AI系统")
        
        # 侧边栏
        self.render_sidebar()
        
        # 主要内容区域
        tab1, tab2, tab3, tab4, tab5 = st.tabs([
            "📊 实时监控", "💼 投资组合", "🎯 交易信号", "📈 回测分析", "⚙️ 系统设置"
        ])
        
        with tab1:
            self.render_realtime_monitoring()
        
        with tab2:
            self.render_portfolio()
        
        with tab3:
            self.render_trading_signals()
        
        with tab4:
            self.render_backtesting()
        
        with tab5:
            self.render_system_settings()
    
    def render_sidebar(self):
        """渲染侧边栏"""
        st.sidebar.header("系统控制")
        
        # 策略引擎控制
        st.sidebar.subheader("策略引擎")
        
        if st.sidebar.button("启动策略", type="primary"):
            if not strategy_engine.is_running:
                # 在新线程中启动策略引擎
                def start_strategy():
                    asyncio.run(strategy_engine.start())
                
                thread = threading.Thread(target=start_strategy, daemon=True)
                thread.start()
                st.sidebar.success("策略引擎启动中...")
            else:
                st.sidebar.warning("策略引擎已在运行")
        
        if st.sidebar.button("停止策略"):
            strategy_engine.stop()
            st.sidebar.success("策略引擎已停止")
        
        # 交易模式
        st.sidebar.subheader("交易模式")
        live_trading = st.sidebar.checkbox("实盘交易", value=False)
        execution_engine.set_live_trading(live_trading)
        
        if live_trading:
            st.sidebar.warning("⚠️ 实盘交易模式已启用")
        else:
            st.sidebar.info("📝 当前为模拟交易模式")
        
        # 系统状态
        st.sidebar.subheader("系统状态")
        status = strategy_engine.get_strategy_status()
        
        if status.get('is_running', False):
            st.sidebar.success("🟢 策略运行中")
        else:
            st.sidebar.error("🔴 策略已停止")
        
        st.sidebar.metric("监控股票数", len(status.get('symbols', [])))
        st.sidebar.metric("持仓数量", status.get('positions', 0))
        
        # 快速操作
        st.sidebar.subheader("快速操作")
        if st.sidebar.button("重置当日统计"):
            risk_manager.reset_daily_stats()
            st.sidebar.success("当日统计已重置")
    
    def render_realtime_monitoring(self):
        """渲染实时监控"""
        st.header("📊 实时监控")
        
        # 获取实时数据
        risk_metrics = risk_manager.get_risk_metrics()
        execution_stats = execution_engine.get_execution_stats()
        
        # 关键指标
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric(
                "组合价值",
                f"${risk_metrics.portfolio_value:,.2f}",
                f"{risk_metrics.daily_pnl:+.2f}"
            )
        
        with col2:
            pnl_color = "profit" if risk_metrics.daily_pnl >= 0 else "loss"
            st.metric(
                "当日盈亏",
                f"${risk_metrics.daily_pnl:+,.2f}",
                f"{(risk_metrics.daily_pnl/risk_metrics.portfolio_value)*100:+.2f}%"
            )
        
        with col3:
            st.metric(
                "最大回撤",
                f"{risk_metrics.max_drawdown:.2%}",
                delta_color="inverse"
            )
        
        with col4:
            risk_color = {
                'low': 'normal',
                'medium': 'normal', 
                'high': 'inverse',
                'critical': 'inverse'
            }.get(risk_metrics.risk_level.value, 'normal')
            
            st.metric(
                "风险等级",
                risk_metrics.risk_level.value.upper(),
                delta_color=risk_color
            )
        
        # 实时图表
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("权益曲线")
            # 这里应该显示实时权益曲线
            # 为演示目的，创建模拟数据
            dates = pd.date_range(start=datetime.now()-timedelta(days=7), end=datetime.now(), freq='H')
            equity_data = pd.DataFrame({
                'timestamp': dates,
                'equity': np.random.normal(100000, 5000, len(dates)).cumsum()
            })
            
            fig = px.line(equity_data, x='timestamp', y='equity', title='权益曲线')
            st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            st.subheader("仓位分布")
            positions = risk_manager.positions
            
            if positions:
                position_data = []
                for symbol, position in positions.items():
                    position_data.append({
                        'symbol': symbol,
                        'value': position.market_value,
                        'pnl': position.unrealized_pnl
                    })
                
                df = pd.DataFrame(position_data)
                fig = px.pie(df, values='value', names='symbol', title='仓位分布')
                st.plotly_chart(fig, use_container_width=True)
            else:
                st.info("当前无持仓")
        
        # 交易统计
        st.subheader("交易统计")
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("总订单数", execution_stats.get('total_orders', 0))
        
        with col2:
            st.metric("成交订单", execution_stats.get('filled_orders', 0))
        
        with col3:
            st.metric("取消订单", execution_stats.get('cancelled_orders', 0))
        
        with col4:
            st.metric("总成交量", f"{execution_stats.get('total_volume', 0):,.0f}")
    
    def render_portfolio(self):
        """渲染投资组合"""
        st.header("💼 投资组合")
        
        positions = risk_manager.positions
        
        if not positions:
            st.info("当前无持仓")
            return
        
        # 持仓表格
        position_data = []
        for symbol, position in positions.items():
            position_data.append({
                '股票代码': symbol,
                '方向': position.side,
                '数量': position.quantity,
                '成本价': f"${position.entry_price:.2f}",
                '当前价': f"${position.current_price:.2f}",
                '市值': f"${position.market_value:,.2f}",
                '未实现盈亏': f"${position.unrealized_pnl:+,.2f}",
                '盈亏比例': f"{position.unrealized_pnl_percent:+.2f}%",
                '止损价': f"${position.stop_loss:.2f}" if position.stop_loss else "未设置",
                '止盈价': f"${position.take_profit:.2f}" if position.take_profit else "未设置"
            })
        
        df = pd.DataFrame(position_data)
        st.dataframe(df, use_container_width=True)
        
        # 持仓分析
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("盈亏分析")
            profit_positions = [p for p in positions.values() if p.unrealized_pnl > 0]
            loss_positions = [p for p in positions.values() if p.unrealized_pnl < 0]
            
            st.metric("盈利持仓", len(profit_positions))
            st.metric("亏损持仓", len(loss_positions))
            
            total_profit = sum(p.unrealized_pnl for p in profit_positions)
            total_loss = sum(p.unrealized_pnl for p in loss_positions)
            
            st.metric("总盈利", f"${total_profit:+,.2f}")
            st.metric("总亏损", f"${total_loss:+,.2f}")
        
        with col2:
            st.subheader("风险指标")
            risk_metrics = risk_manager.get_risk_metrics()
            
            st.metric("总仓位", f"${risk_metrics.total_exposure:,.2f}")
            st.metric("仓位比例", f"{(risk_metrics.total_exposure/risk_metrics.portfolio_value)*100:.1f}%")
            st.metric("VaR (95%)", f"${risk_metrics.var_95:,.2f}")
            st.metric("夏普比率", f"{risk_metrics.sharpe_ratio:.2f}")
    
    def render_trading_signals(self):
        """渲染交易信号"""
        st.header("🎯 交易信号")
        
        # 最近信号
        signals = db_manager.get_trading_signals(limit=50)
        
        if signals:
            signal_data = []
            for signal in signals:
                signal_data.append({
                    '时间': signal['created_at'].strftime('%Y-%m-%d %H:%M:%S'),
                    '股票': signal['symbol'],
                    '信号': signal['signal_type'],
                    '置信度': f"{signal['confidence']:.3f}",
                    '价格': f"${signal['price']:.2f}",
                    '模型': signal['model_name'] or 'Unknown'
                })
            
            df = pd.DataFrame(signal_data)
            st.dataframe(df, use_container_width=True)
            
            # 信号统计
            col1, col2 = st.columns(2)
            
            with col1:
                st.subheader("信号分布")
                signal_counts = pd.Series([s['signal_type'] for s in signals]).value_counts()
                fig = px.bar(x=signal_counts.index, y=signal_counts.values, title='信号类型分布')
                st.plotly_chart(fig, use_container_width=True)
            
            with col2:
                st.subheader("置信度分布")
                confidences = [s['confidence'] for s in signals]
                fig = px.histogram(x=confidences, title='置信度分布', nbins=20)
                st.plotly_chart(fig, use_container_width=True)
        else:
            st.info("暂无交易信号")
        
        # 实时预测
        st.subheader("实时预测")
        
        selected_symbol = st.selectbox("选择股票", SUPPORTED_SYMBOLS)
        
        if st.button("获取预测"):
            with st.spinner("正在预测..."):
                # 这里应该获取实时特征并进行预测
                # 为演示目的，创建模拟预测
                predictions = {
                    'random_forest': {
                        'prediction': np.random.choice([-1, 0, 1]),
                        'confidence': np.random.uniform(0.5, 0.9),
                        'model_type': 'ml'
                    },
                    'xgboost': {
                        'prediction': np.random.choice([-1, 0, 1]),
                        'confidence': np.random.uniform(0.5, 0.9),
                        'model_type': 'ml'
                    }
                }
                
                for model_name, pred in predictions.items():
                    direction = {-1: "下跌", 0: "横盘", 1: "上涨"}[pred['prediction']]
                    confidence = pred['confidence']
                    
                    st.metric(
                        f"{model_name} 预测",
                        direction,
                        f"置信度: {confidence:.3f}"
                    )
    
    def render_backtesting(self):
        """渲染回测分析"""
        st.header("📈 回测分析")
        
        # 回测参数设置
        col1, col2 = st.columns(2)
        
        with col1:
            symbol = st.selectbox("选择股票", SUPPORTED_SYMBOLS, key="backtest_symbol")
            start_date = st.date_input("开始日期", datetime.now() - timedelta(days=90))
            end_date = st.date_input("结束日期", datetime.now())
        
        with col2:
            model_type = st.selectbox("模型类型", ["random_forest", "xgboost", "lstm"])
            initial_capital = st.number_input("初始资金", value=100000, min_value=10000)
        
        if st.button("开始回测"):
            with st.spinner("正在运行回测..."):
                try:
                    # 运行回测
                    result = backtest_engine.run_backtest(
                        symbol=symbol,
                        start_date=datetime.combine(start_date, datetime.min.time()),
                        end_date=datetime.combine(end_date, datetime.min.time()),
                        model_config={'model_type': model_type}
                    )
                    
                    # 显示结果
                    st.success("回测完成！")
                    
                    # 关键指标
                    col1, col2, col3, col4 = st.columns(4)
                    
                    with col1:
                        st.metric("总收益率", f"{result.total_return:.2%}")
                    
                    with col2:
                        st.metric("年化收益率", f"{result.annual_return:.2%}")
                    
                    with col3:
                        st.metric("夏普比率", f"{result.sharpe_ratio:.2f}")
                    
                    with col4:
                        st.metric("最大回撤", f"{result.max_drawdown:.2%}")
                    
                    # 权益曲线
                    st.subheader("权益曲线")
                    fig = px.line(
                        x=result.equity_curve.index,
                        y=result.equity_curve.values,
                        title="权益曲线"
                    )
                    st.plotly_chart(fig, use_container_width=True)
                    
                    # 详细报告
                    st.subheader("详细报告")
                    report = backtest_engine.generate_report(result)
                    st.text(report)
                    
                except Exception as e:
                    st.error(f"回测失败: {e}")
    
    def render_system_settings(self):
        """渲染系统设置"""
        st.header("⚙️ 系统设置")
        
        # 策略参数
        st.subheader("策略参数")
        
        col1, col2 = st.columns(2)
        
        with col1:
            min_confidence = st.slider(
                "最小置信度阈值",
                min_value=0.5,
                max_value=1.0,
                value=0.7,
                step=0.05
            )
            
            prediction_interval = st.number_input(
                "预测间隔(秒)",
                min_value=30,
                max_value=3600,
                value=60
            )
        
        with col2:
            max_position_size = st.slider(
                "最大单笔仓位比例",
                min_value=0.01,
                max_value=0.1,
                value=0.02,
                step=0.01
            )
            
            max_daily_loss = st.slider(
                "最大日损失比例",
                min_value=0.01,
                max_value=0.2,
                value=0.05,
                step=0.01
            )
        
        if st.button("应用设置"):
            strategy_engine.set_min_confidence(min_confidence)
            strategy_engine.set_prediction_interval(prediction_interval)
            risk_manager.max_position_size = max_position_size
            risk_manager.max_daily_loss = max_daily_loss
            st.success("设置已应用")
        
        # 监控股票
        st.subheader("监控股票")
        
        current_symbols = strategy_engine.symbols
        st.write("当前监控股票:", ", ".join(current_symbols))
        
        col1, col2 = st.columns(2)
        
        with col1:
            new_symbol = st.selectbox("添加股票", 
                [s for s in SUPPORTED_SYMBOLS if s not in current_symbols])
            if st.button("添加"):
                strategy_engine.add_symbol(new_symbol)
                st.success(f"已添加 {new_symbol}")
        
        with col2:
            remove_symbol = st.selectbox("移除股票", current_symbols)
            if st.button("移除"):
                strategy_engine.remove_symbol(remove_symbol)
                st.success(f"已移除 {remove_symbol}")
        
        # 模型管理
        st.subheader("模型管理")
        
        models = model_manager.list_models()
        if models:
            model_data = []
            for key, info in models.items():
                model_data.append({
                    '模型名称': key,
                    '股票': info['symbol'],
                    '类型': info['type'],
                    '训练时间': info['trained_at'].strftime('%Y-%m-%d %H:%M:%S'),
                    '准确率': f"{info['performance'].get('accuracy', 0):.3f}"
                })
            
            df = pd.DataFrame(model_data)
            st.dataframe(df, use_container_width=True)
        else:
            st.info("暂无已训练模型")


def main():
    """主函数"""
    dashboard = TradingDashboard()
    dashboard.run()


if __name__ == "__main__":
    main()

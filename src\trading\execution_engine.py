"""
交易执行引擎 - 负责订单执行和管理
"""
import asyncio
import uuid
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import alpaca_trade_api as tradeapi
from loguru import logger

from config.settings import settings
from src.trading.risk_manager import risk_manager
from src.data.database import db_manager


class OrderType(Enum):
    """订单类型"""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"


class OrderSide(Enum):
    """订单方向"""
    BUY = "buy"
    SELL = "sell"


class OrderStatus(Enum):
    """订单状态"""
    PENDING = "pending"
    SUBMITTED = "submitted"
    FILLED = "filled"
    PARTIALLY_FILLED = "partially_filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"


class TimeInForce(Enum):
    """订单有效期"""
    DAY = "day"
    GTC = "gtc"  # Good Till Cancelled
    IOC = "ioc"  # Immediate Or Cancel
    FOK = "fok"  # Fill Or Kill


@dataclass
class Order:
    """订单信息"""
    id: str
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: float
    price: Optional[float] = None
    stop_price: Optional[float] = None
    time_in_force: TimeInForce = TimeInForce.DAY
    status: OrderStatus = OrderStatus.PENDING
    filled_quantity: float = 0.0
    filled_price: Optional[float] = None
    created_at: datetime = None
    updated_at: datetime = None
    broker_order_id: Optional[str] = None
    error_message: Optional[str] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()


class TradingSignal:
    """交易信号"""
    
    def __init__(self, symbol: str, action: str, confidence: float, 
                 price: float, model_name: str = None, features: Dict = None):
        self.symbol = symbol
        self.action = action  # BUY, SELL, HOLD
        self.confidence = confidence
        self.price = price
        self.model_name = model_name
        self.features = features
        self.timestamp = datetime.now()


class ExecutionEngine:
    """交易执行引擎"""
    
    def __init__(self):
        # Alpaca API客户端
        self.alpaca_api = tradeapi.REST(
            settings.ALPACA_API_KEY,
            settings.ALPACA_SECRET_KEY,
            settings.ALPACA_BASE_URL,
            api_version='v2'
        )
        
        # 订单管理
        self.orders: Dict[str, Order] = {}
        self.pending_orders: Dict[str, Order] = {}
        
        # 回调函数
        self.order_callbacks: List[Callable] = []
        self.fill_callbacks: List[Callable] = []
        
        # 执行统计
        self.execution_stats = {
            'total_orders': 0,
            'filled_orders': 0,
            'cancelled_orders': 0,
            'rejected_orders': 0,
            'total_volume': 0.0
        }
        
        # 是否启用实际交易（False为模拟模式）
        self.live_trading = False
    
    def add_order_callback(self, callback: Callable):
        """添加订单状态回调"""
        self.order_callbacks.append(callback)
    
    def add_fill_callback(self, callback: Callable):
        """添加成交回调"""
        self.fill_callbacks.append(callback)
    
    def _notify_order_callbacks(self, order: Order):
        """通知订单状态回调"""
        for callback in self.order_callbacks:
            try:
                callback(order)
            except Exception as e:
                logger.error(f"订单回调执行错误: {e}")
    
    def _notify_fill_callbacks(self, order: Order):
        """通知成交回调"""
        for callback in self.fill_callbacks:
            try:
                callback(order)
            except Exception as e:
                logger.error(f"成交回调执行错误: {e}")
    
    def create_order(self, symbol: str, side: OrderSide, quantity: float,
                    order_type: OrderType = OrderType.MARKET, price: float = None,
                    stop_price: float = None, time_in_force: TimeInForce = TimeInForce.DAY) -> Order:
        """创建订单"""
        try:
            order_id = str(uuid.uuid4())
            
            order = Order(
                id=order_id,
                symbol=symbol,
                side=side,
                order_type=order_type,
                quantity=quantity,
                price=price,
                stop_price=stop_price,
                time_in_force=time_in_force
            )
            
            self.orders[order_id] = order
            logger.info(f"创建订单: {order_id} {symbol} {side.value} {quantity}")
            
            return order
            
        except Exception as e:
            logger.error(f"创建订单失败: {e}")
            raise
    
    def submit_order(self, order: Order) -> bool:
        """提交订单"""
        try:
            # 风险检查
            side_str = "long" if order.side == OrderSide.BUY else "short"
            risk_ok, risk_msg = risk_manager.check_risk_limits(
                order.symbol, order.quantity, order.price or 0, side_str
            )
            
            if not risk_ok:
                order.status = OrderStatus.REJECTED
                order.error_message = risk_msg
                order.updated_at = datetime.now()
                self._notify_order_callbacks(order)
                logger.warning(f"订单被风险管理拒绝: {risk_msg}")
                return False
            
            if self.live_trading:
                # 实际交易模式
                success = self._submit_to_broker(order)
            else:
                # 模拟交易模式
                success = self._simulate_order(order)
            
            if success:
                order.status = OrderStatus.SUBMITTED
                self.pending_orders[order.id] = order
                self.execution_stats['total_orders'] += 1
            else:
                order.status = OrderStatus.REJECTED
            
            order.updated_at = datetime.now()
            self._notify_order_callbacks(order)
            
            return success
            
        except Exception as e:
            logger.error(f"提交订单失败: {e}")
            order.status = OrderStatus.REJECTED
            order.error_message = str(e)
            order.updated_at = datetime.now()
            self._notify_order_callbacks(order)
            return False
    
    def _submit_to_broker(self, order: Order) -> bool:
        """提交订单到券商"""
        try:
            # 构建Alpaca订单参数
            alpaca_order_params = {
                'symbol': order.symbol,
                'qty': order.quantity,
                'side': order.side.value,
                'type': order.order_type.value,
                'time_in_force': order.time_in_force.value
            }
            
            # 添加价格参数
            if order.order_type in [OrderType.LIMIT, OrderType.STOP_LIMIT]:
                alpaca_order_params['limit_price'] = order.price
            
            if order.order_type in [OrderType.STOP, OrderType.STOP_LIMIT]:
                alpaca_order_params['stop_price'] = order.stop_price
            
            # 提交订单
            alpaca_order = self.alpaca_api.submit_order(**alpaca_order_params)
            
            # 更新订单信息
            order.broker_order_id = alpaca_order.id
            
            logger.info(f"订单已提交到Alpaca: {order.id} -> {alpaca_order.id}")
            return True
            
        except Exception as e:
            logger.error(f"提交订单到券商失败: {e}")
            order.error_message = str(e)
            return False
    
    def _simulate_order(self, order: Order) -> bool:
        """模拟订单执行"""
        try:
            # 模拟订单立即成交（简化版本）
            if order.order_type == OrderType.MARKET:
                # 市价单立即成交
                asyncio.create_task(self._simulate_fill(order, order.price or 100.0))
            else:
                # 限价单等待成交
                logger.info(f"模拟限价单等待成交: {order.id}")
            
            return True
            
        except Exception as e:
            logger.error(f"模拟订单失败: {e}")
            return False
    
    async def _simulate_fill(self, order: Order, fill_price: float):
        """模拟订单成交"""
        try:
            # 模拟延迟
            await asyncio.sleep(1)
            
            # 更新订单状态
            order.status = OrderStatus.FILLED
            order.filled_quantity = order.quantity
            order.filled_price = fill_price
            order.updated_at = datetime.now()
            
            # 从待成交订单中移除
            if order.id in self.pending_orders:
                del self.pending_orders[order.id]
            
            # 更新统计
            self.execution_stats['filled_orders'] += 1
            self.execution_stats['total_volume'] += order.quantity
            
            # 通知回调
            self._notify_fill_callbacks(order)
            
            # 更新风险管理器
            side_str = "long" if order.side == OrderSide.BUY else "short"
            if order.side == OrderSide.BUY:
                risk_manager.open_position(order.symbol, order.quantity, fill_price, side_str)
            else:
                risk_manager.close_position(order.symbol, order.quantity, fill_price)
            
            logger.info(f"模拟订单成交: {order.id} {order.symbol} {order.quantity}@{fill_price:.2f}")
            
        except Exception as e:
            logger.error(f"模拟成交失败: {e}")
    
    def cancel_order(self, order_id: str) -> bool:
        """取消订单"""
        try:
            if order_id not in self.orders:
                logger.warning(f"订单不存在: {order_id}")
                return False
            
            order = self.orders[order_id]
            
            if order.status not in [OrderStatus.PENDING, OrderStatus.SUBMITTED]:
                logger.warning(f"订单状态不允许取消: {order.status}")
                return False
            
            if self.live_trading and order.broker_order_id:
                # 取消券商订单
                try:
                    self.alpaca_api.cancel_order(order.broker_order_id)
                except Exception as e:
                    logger.error(f"取消券商订单失败: {e}")
                    return False
            
            # 更新订单状态
            order.status = OrderStatus.CANCELLED
            order.updated_at = datetime.now()
            
            # 从待成交订单中移除
            if order_id in self.pending_orders:
                del self.pending_orders[order_id]
            
            # 更新统计
            self.execution_stats['cancelled_orders'] += 1
            
            # 通知回调
            self._notify_order_callbacks(order)
            
            logger.info(f"订单已取消: {order_id}")
            return True
            
        except Exception as e:
            logger.error(f"取消订单失败: {e}")
            return False
    
    def process_trading_signal(self, signal: TradingSignal) -> Optional[Order]:
        """处理交易信号"""
        try:
            if signal.action == "HOLD":
                logger.info(f"收到持有信号: {signal.symbol}")
                return None
            
            # 计算仓位大小
            quantity = risk_manager.calculate_position_size(
                signal.symbol, signal.price, signal.confidence
            )
            
            if quantity <= 0:
                logger.warning(f"计算仓位为0，跳过交易: {signal.symbol}")
                return None
            
            # 确定订单方向
            side = OrderSide.BUY if signal.action == "BUY" else OrderSide.SELL
            
            # 创建订单
            order = self.create_order(
                symbol=signal.symbol,
                side=side,
                quantity=quantity,
                order_type=OrderType.MARKET
            )
            
            # 提交订单
            if self.submit_order(order):
                # 保存交易信号到数据库
                signal_data = {
                    'symbol': signal.symbol,
                    'signal_type': signal.action,
                    'confidence': signal.confidence,
                    'price': signal.price,
                    'model_name': signal.model_name,
                    'features': str(signal.features) if signal.features else None
                }
                db_manager.save_trading_signal(signal_data)
                
                logger.info(f"处理交易信号成功: {signal.symbol} {signal.action}")
                return order
            else:
                logger.error(f"提交订单失败: {signal.symbol}")
                return None
                
        except Exception as e:
            logger.error(f"处理交易信号失败: {e}")
            return None
    
    def update_order_status(self):
        """更新订单状态（从券商获取）"""
        try:
            if not self.live_trading:
                return
            
            for order in list(self.pending_orders.values()):
                if order.broker_order_id:
                    try:
                        # 获取券商订单状态
                        alpaca_order = self.alpaca_api.get_order(order.broker_order_id)
                        
                        # 更新订单状态
                        old_status = order.status
                        
                        if alpaca_order.status == 'filled':
                            order.status = OrderStatus.FILLED
                            order.filled_quantity = float(alpaca_order.filled_qty)
                            order.filled_price = float(alpaca_order.filled_avg_price or 0)
                        elif alpaca_order.status == 'partially_filled':
                            order.status = OrderStatus.PARTIALLY_FILLED
                            order.filled_quantity = float(alpaca_order.filled_qty)
                            order.filled_price = float(alpaca_order.filled_avg_price or 0)
                        elif alpaca_order.status == 'canceled':
                            order.status = OrderStatus.CANCELLED
                        elif alpaca_order.status == 'rejected':
                            order.status = OrderStatus.REJECTED
                        
                        # 如果状态发生变化，通知回调
                        if old_status != order.status:
                            order.updated_at = datetime.now()
                            self._notify_order_callbacks(order)
                            
                            # 如果订单成交，通知成交回调
                            if order.status == OrderStatus.FILLED:
                                self._notify_fill_callbacks(order)
                                del self.pending_orders[order.id]
                                
                                # 更新统计
                                self.execution_stats['filled_orders'] += 1
                                self.execution_stats['total_volume'] += order.filled_quantity
                        
                    except Exception as e:
                        logger.error(f"更新订单状态失败: {order.id}, {e}")
                        
        except Exception as e:
            logger.error(f"批量更新订单状态失败: {e}")
    
    def get_order(self, order_id: str) -> Optional[Order]:
        """获取订单"""
        return self.orders.get(order_id)
    
    def get_orders(self, symbol: str = None, status: OrderStatus = None) -> List[Order]:
        """获取订单列表"""
        orders = list(self.orders.values())
        
        if symbol:
            orders = [o for o in orders if o.symbol == symbol]
        
        if status:
            orders = [o for o in orders if o.status == status]
        
        return orders
    
    def get_execution_stats(self) -> Dict[str, Any]:
        """获取执行统计"""
        return self.execution_stats.copy()
    
    def set_live_trading(self, enabled: bool):
        """设置实际交易模式"""
        self.live_trading = enabled
        mode = "实际交易" if enabled else "模拟交易"
        logger.info(f"交易模式设置为: {mode}")


# 全局交易执行引擎实例
execution_engine = ExecutionEngine()

#!/bin/bash

# 美股短线交易AI系统 - 安装脚本

set -e

echo "🚀 开始安装美股短线交易AI系统..."

# 检查系统要求
check_requirements() {
    echo "📋 检查系统要求..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        echo "❌ Docker未安装，请先安装Docker"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        echo "❌ Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        echo "❌ Python3未安装，请先安装Python3"
        exit 1
    fi
    
    echo "✅ 系统要求检查通过"
}

# 创建必要目录
create_directories() {
    echo "📁 创建必要目录..."
    
    mkdir -p logs
    mkdir -p models
    mkdir -p data
    mkdir -p grafana/dashboards
    mkdir -p grafana/datasources
    mkdir -p prometheus
    mkdir -p nginx
    
    echo "✅ 目录创建完成"
}

# 配置环境变量
setup_environment() {
    echo "⚙️ 配置环境变量..."
    
    if [ ! -f .env ]; then
        cp .env.example .env
        echo "📝 已创建.env文件，请编辑并填入API密钥"
        echo "⚠️  请在.env文件中配置以下必要参数："
        echo "   - POLYGON_API_KEY"
        echo "   - ALPHA_VANTAGE_API_KEY"
        echo "   - ALPACA_API_KEY"
        echo "   - ALPACA_SECRET_KEY"
        echo "   - POSTGRES_PASSWORD"
        echo "   - INFLUXDB_TOKEN"
        
        read -p "是否现在编辑.env文件? (y/n): " edit_env
        if [ "$edit_env" = "y" ]; then
            ${EDITOR:-nano} .env
        fi
    else
        echo "✅ .env文件已存在"
    fi
}

# 创建Grafana配置
setup_grafana() {
    echo "📊 配置Grafana..."
    
    # 创建数据源配置
    cat > grafana/datasources/influxdb.yml << EOF
apiVersion: 1

datasources:
  - name: InfluxDB
    type: influxdb
    access: proxy
    url: http://influxdb:8086
    database: market_data
    user: trading
    password: \${INFLUXDB_USER_PASSWORD}
    isDefault: true
EOF

    # 创建仪表板配置
    cat > grafana/dashboards/dashboard.yml << EOF
apiVersion: 1

providers:
  - name: 'default'
    orgId: 1
    folder: ''
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /etc/grafana/provisioning/dashboards
EOF

    echo "✅ Grafana配置完成"
}

# 创建Prometheus配置
setup_prometheus() {
    echo "📈 配置Prometheus..."
    
    cat > prometheus/prometheus.yml << EOF
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'trading-ai'
    static_configs:
      - targets: ['trading-ai:8000']
    metrics_path: '/metrics'
    scrape_interval: 5s

  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
EOF

    echo "✅ Prometheus配置完成"
}

# 创建Nginx配置
setup_nginx() {
    echo "🌐 配置Nginx..."
    
    cat > nginx/nginx.conf << EOF
events {
    worker_connections 1024;
}

http {
    upstream trading_api {
        server trading-ai:8000;
    }
    
    upstream trading_dashboard {
        server trading-ai:8501;
    }
    
    upstream grafana {
        server grafana:3000;
    }
    
    server {
        listen 80;
        server_name localhost;
        
        location /api/ {
            proxy_pass http://trading_api/;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
        }
        
        location /dashboard/ {
            proxy_pass http://trading_dashboard/;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
        }
        
        location /grafana/ {
            proxy_pass http://grafana/;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
        }
        
        location / {
            proxy_pass http://trading_dashboard/;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
        }
    }
}
EOF

    echo "✅ Nginx配置完成"
}

# 构建和启动服务
start_services() {
    echo "🐳 构建和启动Docker服务..."
    
    # 构建镜像
    docker-compose build
    
    # 启动服务
    docker-compose up -d
    
    echo "⏳ 等待服务启动..."
    sleep 30
    
    # 检查服务状态
    docker-compose ps
    
    echo "✅ 服务启动完成"
}

# 初始化数据库
initialize_database() {
    echo "🗄️ 初始化数据库..."
    
    # 等待数据库启动
    echo "⏳ 等待数据库启动..."
    sleep 10
    
    # 初始化系统
    docker exec trading-ai-app python main.py init
    
    echo "✅ 数据库初始化完成"
}

# 显示访问信息
show_access_info() {
    echo ""
    echo "🎉 安装完成！"
    echo ""
    echo "📱 访问地址："
    echo "   Web仪表板: http://localhost:8501"
    echo "   API文档:   http://localhost:8000/docs"
    echo "   Grafana:   http://localhost:3000 (admin/admin)"
    echo "   Prometheus: http://localhost:9090"
    echo ""
    echo "🔧 管理命令："
    echo "   查看日志: docker-compose logs -f trading-ai"
    echo "   停止服务: docker-compose down"
    echo "   重启服务: docker-compose restart"
    echo ""
    echo "📚 下一步："
    echo "   1. 编辑.env文件配置API密钥"
    echo "   2. 训练模型: docker exec trading-ai-app python main.py train"
    echo "   3. 启动策略: docker exec trading-ai-app python main.py strategy"
    echo ""
    echo "⚠️  风险提示："
    echo "   - 默认为模拟交易模式"
    echo "   - 实盘交易前请充分测试"
    echo "   - 投资有风险，入市需谨慎"
}

# 主函数
main() {
    check_requirements
    create_directories
    setup_environment
    setup_grafana
    setup_prometheus
    setup_nginx
    start_services
    initialize_database
    show_access_info
}

# 执行安装
main

"""
策略引擎 - 整合数据、模型和交易执行
"""
import asyncio
import threading
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from loguru import logger

from src.data.market_data import market_data_manager
from src.data.data_processor import data_processor
from src.data.database import db_manager
from src.models.model_manager import model_manager
from src.trading.risk_manager import risk_manager
from src.trading.execution_engine import execution_engine, TradingSignal
from config.settings import settings, SUPPORTED_SYMBOLS


class StrategyEngine:
    """策略引擎"""
    
    def __init__(self):
        self.is_running = False
        self.symbols = SUPPORTED_SYMBOLS[:8]  # 限制交易股票数量
        self.prediction_interval = 60  # 预测间隔（秒）
        self.last_predictions = {}
        self.market_data_buffer = {}
        
        # 策略参数
        self.min_confidence = settings.PREDICTION_CONFIDENCE_THRESHOLD
        self.position_hold_time = timedelta(minutes=30)  # 最小持仓时间
        
        # 注册回调
        market_data_manager.add_callback(self._on_market_data)
        execution_engine.add_fill_callback(self._on_order_filled)
        
        # 启动定时任务
        self.scheduler_thread = None
    
    def _on_market_data(self, data: Dict[str, Any]):
        """市场数据回调"""
        try:
            symbol = data.get('symbol')
            if symbol and symbol in self.symbols:
                # 更新数据缓冲区
                if symbol not in self.market_data_buffer:
                    self.market_data_buffer[symbol] = []
                
                self.market_data_buffer[symbol].append(data)
                
                # 保持最近1000个数据点
                if len(self.market_data_buffer[symbol]) > 1000:
                    self.market_data_buffer[symbol].pop(0)
                
                # 更新风险管理器的价格
                if 'price' in data:
                    risk_manager.update_positions({symbol: data['price']})
                
                # 写入数据库
                db_manager.write_market_data(symbol, data)
                
        except Exception as e:
            logger.error(f"处理市场数据失败: {e}")
    
    def _on_order_filled(self, order):
        """订单成交回调"""
        try:
            # 保存交易记录到数据库
            trade_data = {
                'symbol': order.symbol,
                'side': order.side.value.upper(),
                'quantity': order.filled_quantity,
                'price': order.filled_price,
                'order_id': order.broker_order_id or order.id,
                'status': 'FILLED',
                'executed_at': order.updated_at
            }
            db_manager.save_trade(trade_data)
            
            logger.info(f"交易记录已保存: {order.symbol} {order.side.value} {order.filled_quantity}@{order.filled_price}")
            
        except Exception as e:
            logger.error(f"保存交易记录失败: {e}")
    
    async def start(self):
        """启动策略引擎"""
        try:
            if self.is_running:
                logger.warning("策略引擎已在运行")
                return
            
            self.is_running = True
            logger.info("启动策略引擎")
            
            # 启动实时数据流
            await market_data_manager.start_realtime_data(self.symbols)
            
            # 启动定时预测任务
            self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
            self.scheduler_thread.start()
            
            # 主循环
            while self.is_running:
                try:
                    # 检查止损止盈
                    await self._check_stop_loss_take_profit()
                    
                    # 更新订单状态
                    execution_engine.update_order_status()
                    
                    # 等待
                    await asyncio.sleep(5)
                    
                except Exception as e:
                    logger.error(f"策略引擎主循环错误: {e}")
                    await asyncio.sleep(10)
            
        except Exception as e:
            logger.error(f"启动策略引擎失败: {e}")
            self.is_running = False
    
    def stop(self):
        """停止策略引擎"""
        self.is_running = False
        logger.info("策略引擎已停止")
    
    def _run_scheduler(self):
        """运行定时任务"""
        import time
        
        while self.is_running:
            try:
                # 执行预测
                asyncio.run(self._run_predictions())
                
                # 等待下一个预测间隔
                time.sleep(self.prediction_interval)
                
            except Exception as e:
                logger.error(f"定时任务执行错误: {e}")
                time.sleep(30)
    
    async def _run_predictions(self):
        """运行预测"""
        try:
            for symbol in self.symbols:
                try:
                    await self._predict_and_trade(symbol)
                except Exception as e:
                    logger.error(f"预测 {symbol} 失败: {e}")
                    
        except Exception as e:
            logger.error(f"批量预测失败: {e}")
    
    async def _predict_and_trade(self, symbol: str):
        """预测并交易"""
        try:
            # 获取历史数据
            end_time = datetime.now()
            start_time = end_time - timedelta(days=30)
            
            df = db_manager.query_market_data(symbol, start_time, end_time)
            
            if df.empty or len(df) < 100:
                logger.warning(f"{symbol} 历史数据不足")
                return
            
            # 数据预处理
            processed_df = data_processor.clean_data(df)
            processed_df = data_processor.calculate_technical_indicators(processed_df)
            processed_df = data_processor.calculate_price_features(processed_df)
            processed_df = data_processor.create_features_for_ml(processed_df)
            
            if processed_df.empty:
                logger.warning(f"{symbol} 数据处理后为空")
                return
            
            # 获取最新特征
            latest_features = processed_df.iloc[-1].values
            
            # 使用模型预测
            predictions = model_manager.predict(symbol, latest_features)
            
            if not predictions:
                logger.warning(f"{symbol} 没有可用的预测模型")
                return
            
            # 处理预测结果
            await self._process_predictions(symbol, predictions, processed_df.iloc[-1]['close'])
            
        except Exception as e:
            logger.error(f"预测和交易 {symbol} 失败: {e}")
    
    async def _process_predictions(self, symbol: str, predictions: Dict[str, Any], current_price: float):
        """处理预测结果"""
        try:
            # 获取集成预测
            ensemble_pred = predictions.get('ensemble')
            if not ensemble_pred:
                # 如果没有集成预测，使用最佳单模型
                best_pred = None
                best_confidence = 0
                
                for model_type, pred in predictions.items():
                    if pred.get('confidence', 0) > best_confidence:
                        best_confidence = pred.get('confidence', 0)
                        best_pred = pred
                
                if best_pred:
                    ensemble_pred = best_pred
                else:
                    logger.warning(f"{symbol} 没有有效预测")
                    return
            
            prediction = ensemble_pred.get('prediction', 0)
            confidence = ensemble_pred.get('confidence', 0)
            
            # 检查置信度阈值
            if confidence < self.min_confidence:
                logger.info(f"{symbol} 预测置信度 {confidence:.3f} 低于阈值 {self.min_confidence}")
                return
            
            # 转换预测为交易信号
            if prediction == 1:  # 上涨
                action = "BUY"
            elif prediction == -1:  # 下跌
                action = "SELL"
            else:  # 横盘
                action = "HOLD"
            
            # 检查是否需要交易
            if await self._should_trade(symbol, action, confidence):
                # 创建交易信号
                signal = TradingSignal(
                    symbol=symbol,
                    action=action,
                    confidence=confidence,
                    price=current_price,
                    model_name="ensemble",
                    features=ensemble_pred.get('individual_predictions')
                )
                
                # 执行交易
                order = execution_engine.process_trading_signal(signal)
                
                if order:
                    logger.info(f"生成交易信号: {symbol} {action} 置信度={confidence:.3f}")
                    
                    # 记录预测
                    self.last_predictions[symbol] = {
                        'prediction': prediction,
                        'confidence': confidence,
                        'action': action,
                        'timestamp': datetime.now(),
                        'price': current_price
                    }
            
        except Exception as e:
            logger.error(f"处理预测结果失败: {e}")
    
    async def _should_trade(self, symbol: str, action: str, confidence: float) -> bool:
        """判断是否应该交易"""
        try:
            # 检查是否在交易时间
            now = datetime.now()
            market_open = now.replace(hour=9, minute=30, second=0, microsecond=0)
            market_close = now.replace(hour=16, minute=0, second=0, microsecond=0)
            
            if not (market_open <= now <= market_close):
                return False
            
            # 检查最近是否有相同的预测
            if symbol in self.last_predictions:
                last_pred = self.last_predictions[symbol]
                time_diff = now - last_pred['timestamp']
                
                # 如果最近30分钟内有相同预测，跳过
                if (time_diff < timedelta(minutes=30) and 
                    last_pred['action'] == action):
                    return False
            
            # 检查当前仓位
            current_positions = risk_manager.positions
            
            if action == "BUY":
                # 如果已有多头仓位，不再买入
                if (symbol in current_positions and 
                    current_positions[symbol].side == "long"):
                    return False
            
            elif action == "SELL":
                # 如果没有多头仓位，不能卖出
                if (symbol not in current_positions or 
                    current_positions[symbol].side != "long"):
                    return False
            
            # 检查风险限制
            risk_metrics = risk_manager.get_risk_metrics()
            if risk_metrics.risk_level.value in ['high', 'critical']:
                logger.warning(f"风险等级过高，暂停交易: {risk_metrics.risk_level.value}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"判断是否交易失败: {e}")
            return False
    
    async def _check_stop_loss_take_profit(self):
        """检查止损止盈"""
        try:
            signals = risk_manager.check_stop_loss_take_profit()
            
            for signal in signals:
                symbol = signal['symbol']
                action = signal['action']
                price = signal['price']
                quantity = signal['quantity']
                reason = signal['reason']
                
                logger.info(f"触发{action}: {symbol} {quantity}@{price:.2f} - {reason}")
                
                # 创建平仓信号
                trading_signal = TradingSignal(
                    symbol=symbol,
                    action="SELL",  # 平仓都是卖出
                    confidence=1.0,  # 止损止盈的置信度为100%
                    price=price,
                    model_name="risk_management"
                )
                
                # 执行平仓
                execution_engine.process_trading_signal(trading_signal)
                
        except Exception as e:
            logger.error(f"检查止损止盈失败: {e}")
    
    def get_strategy_status(self) -> Dict[str, Any]:
        """获取策略状态"""
        try:
            risk_metrics = risk_manager.get_risk_metrics()
            execution_stats = execution_engine.get_execution_stats()
            
            return {
                'is_running': self.is_running,
                'symbols': self.symbols,
                'positions': len(risk_manager.positions),
                'portfolio_value': risk_metrics.portfolio_value,
                'daily_pnl': risk_metrics.daily_pnl,
                'risk_level': risk_metrics.risk_level.value,
                'total_orders': execution_stats['total_orders'],
                'filled_orders': execution_stats['filled_orders'],
                'last_predictions': self.last_predictions
            }
            
        except Exception as e:
            logger.error(f"获取策略状态失败: {e}")
            return {}
    
    def add_symbol(self, symbol: str):
        """添加交易股票"""
        if symbol not in self.symbols:
            self.symbols.append(symbol)
            logger.info(f"添加交易股票: {symbol}")
    
    def remove_symbol(self, symbol: str):
        """移除交易股票"""
        if symbol in self.symbols:
            self.symbols.remove(symbol)
            logger.info(f"移除交易股票: {symbol}")
    
    def set_prediction_interval(self, interval: int):
        """设置预测间隔"""
        self.prediction_interval = max(30, interval)  # 最小30秒
        logger.info(f"设置预测间隔: {self.prediction_interval}秒")
    
    def set_min_confidence(self, confidence: float):
        """设置最小置信度"""
        self.min_confidence = max(0.5, min(1.0, confidence))
        logger.info(f"设置最小置信度: {self.min_confidence}")


# 全局策略引擎实例
strategy_engine = StrategyEngine()

"""
美股短线交易AI系统 - 演示脚本
"""
import asyncio
import sys
import os
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置简单日志
logger.remove()
logger.add(sys.stdout, format="<green>{time:HH:mm:ss}</green> | <level>{level}</level> | {message}")

def create_demo_data():
    """创建演示数据"""
    logger.info("创建演示数据...")
    
    # 创建模拟的股票价格数据
    dates = pd.date_range(start=datetime.now() - timedelta(days=30), end=datetime.now(), freq='1min')
    
    # 生成AAPL模拟数据
    np.random.seed(42)
    returns = np.random.normal(0.0001, 0.02, len(dates))
    prices = 150 * np.exp(np.cumsum(returns))
    
    df = pd.DataFrame(index=dates)
    df['close'] = prices
    df['open'] = df['close'].shift(1).fillna(df['close'].iloc[0])
    df['high'] = np.maximum(df['open'], df['close']) * (1 + np.random.uniform(0, 0.01, len(df)))
    df['low'] = np.minimum(df['open'], df['close']) * (1 - np.random.uniform(0, 0.01, len(df)))
    df['volume'] = np.random.randint(1000, 10000, len(df))
    
    return df

def demo_data_processing():
    """演示数据处理"""
    logger.info("🔄 演示数据处理功能...")
    
    try:
        from src.data.data_processor import data_processor
        
        # 创建测试数据
        df = create_demo_data()
        logger.info(f"原始数据: {len(df)} 行")
        
        # 数据清洗
        cleaned_df = data_processor.clean_data(df)
        logger.info(f"清洗后数据: {len(cleaned_df)} 行")
        
        # 计算技术指标
        processed_df = data_processor.calculate_technical_indicators(cleaned_df)
        logger.info(f"技术指标: {len([col for col in processed_df.columns if col not in df.columns])} 个")
        
        # 特征工程
        feature_df = data_processor.create_features_for_ml(processed_df)
        logger.info(f"机器学习特征: {len(feature_df.columns)} 个")
        
        logger.success("✅ 数据处理演示完成")
        return feature_df
        
    except Exception as e:
        logger.error(f"❌ 数据处理演示失败: {e}")
        return None

def demo_model_training():
    """演示模型训练"""
    logger.info("🤖 演示模型训练功能...")
    
    try:
        from src.models.model_manager import model_manager
        from src.data.data_processor import data_processor
        
        # 准备数据
        df = create_demo_data()
        processed_df = data_processor.clean_data(df)
        processed_df = data_processor.calculate_technical_indicators(processed_df)
        processed_df = data_processor.calculate_price_features(processed_df)
        processed_df = data_processor.create_features_for_ml(processed_df)
        processed_df = data_processor.create_target_variable(processed_df)
        
        if len(processed_df) < 100:
            logger.warning("数据量不足，跳过模型训练演示")
            return
        
        # 训练随机森林模型
        logger.info("训练随机森林模型...")
        result = model_manager.train_ml_model("DEMO", processed_df, "random_forest")
        
        if result:
            accuracy = result['evaluation_results']['accuracy']
            logger.success(f"✅ 随机森林模型训练完成，准确率: {accuracy:.3f}")
        
        # 演示预测
        logger.info("演示模型预测...")
        latest_features = processed_df.iloc[-1].drop(['target_direction', 'target_return', 'target_binary']).values
        predictions = model_manager.predict("DEMO", latest_features)
        
        if predictions:
            logger.info(f"预测结果: {predictions}")
            logger.success("✅ 模型预测演示完成")
        
    except Exception as e:
        logger.error(f"❌ 模型训练演示失败: {e}")

def demo_risk_management():
    """演示风险管理"""
    logger.info("⚖️ 演示风险管理功能...")
    
    try:
        from src.trading.risk_manager import risk_manager
        
        # 重置风险管理器
        risk_manager.current_capital = 100000
        risk_manager.positions.clear()
        
        logger.info(f"初始资金: ${risk_manager.current_capital:,.2f}")
        
        # 计算仓位大小
        symbol = "DEMO"
        price = 150.0
        confidence = 0.8
        
        quantity = risk_manager.calculate_position_size(symbol, price, confidence)
        logger.info(f"建议仓位: {quantity:.0f} 股 (价值: ${quantity * price:,.2f})")
        
        # 开仓
        success = risk_manager.open_position(symbol, quantity, price, "long")
        if success:
            logger.success(f"✅ 开仓成功: {symbol} {quantity:.0f}@${price:.2f}")
        
        # 更新价格
        new_price = 155.0
        risk_manager.update_positions({symbol: new_price})
        logger.info(f"价格更新: ${price:.2f} -> ${new_price:.2f}")
        
        # 获取风险指标
        metrics = risk_manager.get_risk_metrics()
        logger.info(f"组合价值: ${metrics.portfolio_value:,.2f}")
        logger.info(f"当日盈亏: ${metrics.daily_pnl:+,.2f}")
        logger.info(f"风险等级: {metrics.risk_level.value}")
        
        # 平仓
        success = risk_manager.close_position(symbol, quantity, new_price)
        if success:
            logger.success(f"✅ 平仓成功，盈亏: ${(new_price - price) * quantity:+,.2f}")
        
        logger.success("✅ 风险管理演示完成")
        
    except Exception as e:
        logger.error(f"❌ 风险管理演示失败: {e}")

def demo_trading_execution():
    """演示交易执行"""
    logger.info("💼 演示交易执行功能...")
    
    try:
        from src.trading.execution_engine import execution_engine, TradingSignal, OrderSide, OrderType
        
        # 设置为模拟模式
        execution_engine.set_live_trading(False)
        
        # 创建交易信号
        signal = TradingSignal(
            symbol="DEMO",
            action="BUY",
            confidence=0.85,
            price=150.0,
            model_name="demo_model"
        )
        
        logger.info(f"交易信号: {signal.action} {signal.symbol} 置信度={signal.confidence:.2f}")
        
        # 处理交易信号
        order = execution_engine.process_trading_signal(signal)
        
        if order:
            logger.success(f"✅ 订单创建成功: {order.id}")
            logger.info(f"订单详情: {order.symbol} {order.side.value} {order.quantity}")
        else:
            logger.warning("订单创建失败")
        
        # 获取执行统计
        stats = execution_engine.get_execution_stats()
        logger.info(f"执行统计: {stats}")
        
        logger.success("✅ 交易执行演示完成")
        
    except Exception as e:
        logger.error(f"❌ 交易执行演示失败: {e}")

def demo_backtesting():
    """演示回测功能"""
    logger.info("📈 演示回测功能...")
    
    try:
        from src.backtesting.backtest_engine import backtest_engine
        
        # 运行简单回测
        start_date = datetime.now() - timedelta(days=7)
        end_date = datetime.now() - timedelta(days=1)
        
        logger.info(f"回测期间: {start_date.date()} 到 {end_date.date()}")
        
        result = backtest_engine.run_backtest(
            symbol="DEMO",
            start_date=start_date,
            end_date=end_date,
            model_config={'model_type': 'random_forest'}
        )
        
        if result:
            logger.success("✅ 回测完成")
            logger.info(f"总收益率: {result.total_return:.2%}")
            logger.info(f"夏普比率: {result.sharpe_ratio:.2f}")
            logger.info(f"最大回撤: {result.max_drawdown:.2%}")
            logger.info(f"总交易次数: {result.total_trades}")
            logger.info(f"胜率: {result.win_rate:.2%}")
        
    except Exception as e:
        logger.error(f"❌ 回测演示失败: {e}")

async def demo_strategy_engine():
    """演示策略引擎"""
    logger.info("🎯 演示策略引擎功能...")
    
    try:
        from src.trading.strategy_engine import strategy_engine
        
        # 获取策略状态
        status = strategy_engine.get_strategy_status()
        logger.info(f"策略状态: {'运行中' if status.get('is_running') else '已停止'}")
        logger.info(f"监控股票: {status.get('symbols', [])}")
        
        # 设置策略参数
        strategy_engine.set_min_confidence(0.7)
        strategy_engine.set_prediction_interval(60)
        logger.info("策略参数已设置")
        
        # 添加演示股票
        strategy_engine.add_symbol("DEMO")
        logger.info("已添加演示股票")
        
        logger.success("✅ 策略引擎演示完成")
        
    except Exception as e:
        logger.error(f"❌ 策略引擎演示失败: {e}")

def show_system_info():
    """显示系统信息"""
    logger.info("📋 系统信息:")
    logger.info("=" * 50)
    logger.info("🤖 美股短线交易AI系统")
    logger.info("📊 功能模块:")
    logger.info("  • 实时数据获取 (Polygon.io, Alpha Vantage, Yahoo Finance)")
    logger.info("  • AI预测模型 (LSTM + 传统ML)")
    logger.info("  • 智能风险管理 (动态仓位、止损止盈)")
    logger.info("  • 自动交易执行 (Alpaca API)")
    logger.info("  • 实时监控界面 (Streamlit + FastAPI)")
    logger.info("  • 回测分析系统")
    logger.info("")
    logger.info("🚀 启动方式:")
    logger.info("  python main.py strategy    # 启动交易策略")
    logger.info("  python main.py dashboard   # 启动Web界面")
    logger.info("  python main.py api         # 启动API服务")
    logger.info("  python main.py train       # 训练模型")
    logger.info("  python main.py backtest    # 运行回测")
    logger.info("")
    logger.info("🌐 访问地址:")
    logger.info("  Web仪表板: http://localhost:8501")
    logger.info("  API文档:   http://localhost:8000/docs")
    logger.info("")
    logger.info("⚠️  风险提示:")
    logger.info("  • 默认为模拟交易模式")
    logger.info("  • 实盘交易前请充分测试")
    logger.info("  • 投资有风险，入市需谨慎")
    logger.info("=" * 50)

async def main():
    """主演示函数"""
    logger.info("🎉 欢迎使用美股短线交易AI系统演示!")
    
    show_system_info()
    
    logger.info("\n🔥 开始功能演示...")
    
    # 1. 数据处理演示
    demo_data_processing()
    
    # 2. 模型训练演示
    demo_model_training()
    
    # 3. 风险管理演示
    demo_risk_management()
    
    # 4. 交易执行演示
    demo_trading_execution()
    
    # 5. 回测演示
    demo_backtesting()
    
    # 6. 策略引擎演示
    await demo_strategy_engine()
    
    logger.success("\n🎊 演示完成!")
    logger.info("\n📚 下一步:")
    logger.info("1. 配置.env文件中的API密钥")
    logger.info("2. 运行 python main.py train 训练模型")
    logger.info("3. 运行 python main.py dashboard 启动Web界面")
    logger.info("4. 运行 python main.py strategy 启动实时交易")

if __name__ == "__main__":
    asyncio.run(main())

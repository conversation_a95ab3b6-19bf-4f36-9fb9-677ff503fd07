"""
美股短线交易AI系统 - 主程序入口
"""
import asyncio
import argparse
import sys
import os
from pathlib import Path
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.trading.strategy_engine import strategy_engine
from src.data.market_data import market_data_manager
from src.data.database import db_manager
from src.models.model_manager import model_manager
from config.settings import settings


def setup_logging():
    """设置日志"""
    # 移除默认处理器
    logger.remove()
    
    # 添加控制台输出
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level=settings.LOG_LEVEL
    )
    
    # 添加文件输出
    logger.add(
        "logs/trading_ai.log",
        rotation="1 day",
        retention="30 days",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        level=settings.LOG_LEVEL
    )
    
    logger.info("日志系统初始化完成")


def check_environment():
    """检查环境配置"""
    logger.info("检查环境配置...")
    
    # 检查必要的环境变量
    required_vars = [
        'POLYGON_API_KEY',
        'ALPHA_VANTAGE_API_KEY', 
        'ALPACA_API_KEY',
        'ALPACA_SECRET_KEY',
        'POSTGRES_PASSWORD',
        'INFLUXDB_TOKEN'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not getattr(settings, var, None):
            missing_vars.append(var)
    
    if missing_vars:
        logger.error(f"缺少必要的环境变量: {', '.join(missing_vars)}")
        logger.info("请检查 .env 文件或环境变量设置")
        return False
    
    # 创建必要的目录
    os.makedirs("logs", exist_ok=True)
    os.makedirs("models", exist_ok=True)
    os.makedirs("data", exist_ok=True)
    
    logger.info("环境检查完成")
    return True


async def initialize_system():
    """初始化系统"""
    logger.info("初始化交易AI系统...")
    
    try:
        # 测试数据库连接
        logger.info("测试数据库连接...")
        db_manager.create_tables()
        
        # 初始化市场数据管理器
        logger.info("初始化市场数据管理器...")
        # 这里可以添加数据源连接测试
        
        logger.info("系统初始化完成")
        return True
        
    except Exception as e:
        logger.error(f"系统初始化失败: {e}")
        return False


async def run_strategy():
    """运行交易策略"""
    logger.info("启动交易策略引擎...")
    
    try:
        await strategy_engine.start()
        
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在停止...")
        strategy_engine.stop()
        
    except Exception as e:
        logger.error(f"策略运行错误: {e}")
        strategy_engine.stop()


def run_web_dashboard():
    """运行Web仪表板"""
    logger.info("启动Web仪表板...")
    
    try:
        import streamlit.web.cli as stcli
        import sys
        
        # 设置Streamlit参数
        sys.argv = [
            "streamlit",
            "run",
            "src/web/dashboard.py",
            "--server.port=8501",
            "--server.address=0.0.0.0",
            "--server.headless=true"
        ]
        
        stcli.main()
        
    except Exception as e:
        logger.error(f"Web仪表板启动失败: {e}")


def run_api_server():
    """运行API服务器"""
    logger.info("启动API服务器...")
    
    try:
        import uvicorn
        
        uvicorn.run(
            "src.web.api:app",
            host="0.0.0.0",
            port=8000,
            reload=False,
            log_level="info"
        )
        
    except Exception as e:
        logger.error(f"API服务器启动失败: {e}")


async def train_models():
    """训练模型"""
    logger.info("开始训练模型...")
    
    from datetime import datetime, timedelta
    from config.settings import SUPPORTED_SYMBOLS
    
    try:
        # 选择几个主要股票进行训练
        symbols = SUPPORTED_SYMBOLS[:5]  # 训练前5个股票
        
        for symbol in symbols:
            logger.info(f"训练 {symbol} 的模型...")
            
            # 获取历史数据
            end_date = datetime.now()
            start_date = end_date - timedelta(days=90)
            
            df = db_manager.query_market_data(symbol, start_date, end_date)
            
            if df.empty:
                logger.warning(f"{symbol} 没有历史数据，跳过训练")
                continue
            
            # 训练随机森林模型
            try:
                result = model_manager.train_ml_model(symbol, df, "random_forest")
                logger.info(f"{symbol} 随机森林模型训练完成，准确率: {result['evaluation_results']['accuracy']:.3f}")
            except Exception as e:
                logger.error(f"{symbol} 随机森林模型训练失败: {e}")
            
            # 训练XGBoost模型
            try:
                result = model_manager.train_ml_model(symbol, df, "xgboost")
                logger.info(f"{symbol} XGBoost模型训练完成，准确率: {result['evaluation_results']['accuracy']:.3f}")
            except Exception as e:
                logger.error(f"{symbol} XGBoost模型训练失败: {e}")
        
        logger.info("模型训练完成")
        
    except Exception as e:
        logger.error(f"模型训练失败: {e}")


def run_backtest():
    """运行回测"""
    logger.info("开始回测...")
    
    from datetime import datetime, timedelta
    from src.backtesting.backtest_engine import backtest_engine
    
    try:
        symbol = "AAPL"
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        
        result = backtest_engine.run_backtest(
            symbol=symbol,
            start_date=start_date,
            end_date=end_date,
            model_config={'model_type': 'random_forest'}
        )
        
        logger.info(f"回测完成:")
        logger.info(f"总收益率: {result.total_return:.2%}")
        logger.info(f"夏普比率: {result.sharpe_ratio:.2f}")
        logger.info(f"最大回撤: {result.max_drawdown:.2%}")
        logger.info(f"胜率: {result.win_rate:.2%}")
        
        # 生成报告
        report = backtest_engine.generate_report(result)
        print(report)
        
    except Exception as e:
        logger.error(f"回测失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="美股短线交易AI系统")
    parser.add_argument(
        "command",
        choices=["strategy", "dashboard", "api", "train", "backtest", "init"],
        help="运行模式"
    )
    parser.add_argument("--debug", action="store_true", help="调试模式")
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging()
    
    if args.debug:
        logger.info("调试模式已启用")
    
    # 检查环境
    if not check_environment():
        sys.exit(1)
    
    # 根据命令执行相应操作
    if args.command == "init":
        # 初始化系统
        success = asyncio.run(initialize_system())
        if success:
            logger.info("系统初始化成功")
        else:
            logger.error("系统初始化失败")
            sys.exit(1)
    
    elif args.command == "strategy":
        # 运行交易策略
        asyncio.run(initialize_system())
        asyncio.run(run_strategy())
    
    elif args.command == "dashboard":
        # 运行Web仪表板
        run_web_dashboard()
    
    elif args.command == "api":
        # 运行API服务器
        run_api_server()
    
    elif args.command == "train":
        # 训练模型
        asyncio.run(initialize_system())
        asyncio.run(train_models())
    
    elif args.command == "backtest":
        # 运行回测
        asyncio.run(initialize_system())
        run_backtest()


if __name__ == "__main__":
    main()

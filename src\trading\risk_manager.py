"""
风险管理系统 - 负责仓位管理、止损止盈、风险评估
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
from loguru import logger

from config.settings import settings
from src.data.database import db_manager


class RiskLevel(Enum):
    """风险等级"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class Position:
    """持仓信息"""
    symbol: str
    quantity: float
    entry_price: float
    current_price: float
    side: str  # "long" or "short"
    entry_time: datetime
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    
    @property
    def market_value(self) -> float:
        """市值"""
        return abs(self.quantity) * self.current_price
    
    @property
    def unrealized_pnl(self) -> float:
        """未实现盈亏"""
        if self.side == "long":
            return self.quantity * (self.current_price - self.entry_price)
        else:
            return self.quantity * (self.entry_price - self.current_price)
    
    @property
    def unrealized_pnl_percent(self) -> float:
        """未实现盈亏百分比"""
        if self.entry_price == 0:
            return 0
        return self.unrealized_pnl / (abs(self.quantity) * self.entry_price) * 100


@dataclass
class RiskMetrics:
    """风险指标"""
    total_exposure: float
    max_position_size: float
    current_drawdown: float
    max_drawdown: float
    var_95: float  # 95% VaR
    sharpe_ratio: float
    risk_level: RiskLevel
    daily_pnl: float
    portfolio_value: float


class RiskManager:
    """风险管理器"""
    
    def __init__(self, initial_capital: float = 100000):
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.positions: Dict[str, Position] = {}
        self.trade_history: List[Dict] = []
        self.daily_returns: List[float] = []
        self.max_drawdown = 0.0
        self.peak_value = initial_capital
        
        # 风险参数
        self.max_position_size = settings.MAX_POSITION_SIZE
        self.max_daily_loss = settings.MAX_DAILY_LOSS
        self.stop_loss_percent = settings.STOP_LOSS_PERCENT
        self.take_profit_percent = settings.TAKE_PROFIT_PERCENT
        
        # 当日统计
        self.daily_pnl = 0.0
        self.daily_trades = 0
        self.daily_start_capital = initial_capital
    
    def calculate_position_size(self, symbol: str, price: float, 
                              confidence: float, volatility: float = None) -> float:
        """计算仓位大小"""
        try:
            # 基础仓位大小（基于资金管理）
            base_position_value = self.current_capital * self.max_position_size
            base_quantity = base_position_value / price
            
            # 基于置信度调整
            confidence_multiplier = min(confidence, 1.0)
            adjusted_quantity = base_quantity * confidence_multiplier
            
            # 基于波动率调整（如果提供）
            if volatility is not None:
                # 高波动率减少仓位
                volatility_multiplier = max(0.5, 1.0 - volatility)
                adjusted_quantity *= volatility_multiplier
            
            # 检查是否超过最大仓位限制
            max_quantity = (self.current_capital * self.max_position_size) / price
            final_quantity = min(adjusted_quantity, max_quantity)
            
            # 检查资金是否充足
            required_capital = final_quantity * price
            if required_capital > self.current_capital * 0.95:  # 保留5%现金
                final_quantity = (self.current_capital * 0.95) / price
            
            logger.info(f"{symbol} 计算仓位: 价格={price:.2f}, 置信度={confidence:.2f}, 数量={final_quantity:.2f}")
            return final_quantity
            
        except Exception as e:
            logger.error(f"计算仓位大小失败: {e}")
            return 0.0
    
    def check_risk_limits(self, symbol: str, quantity: float, price: float, side: str) -> Tuple[bool, str]:
        """检查风险限制"""
        try:
            # 检查单笔交易大小
            trade_value = quantity * price
            max_trade_value = self.current_capital * self.max_position_size
            
            if trade_value > max_trade_value:
                return False, f"交易金额 {trade_value:.2f} 超过最大限制 {max_trade_value:.2f}"
            
            # 检查总仓位
            total_exposure = self.get_total_exposure()
            new_exposure = total_exposure + trade_value
            max_total_exposure = self.current_capital * 0.95  # 最大95%仓位
            
            if new_exposure > max_total_exposure:
                return False, f"总仓位 {new_exposure:.2f} 将超过最大限制 {max_total_exposure:.2f}"
            
            # 检查当日损失
            if self.daily_pnl < 0:
                max_daily_loss_amount = self.current_capital * self.max_daily_loss
                if abs(self.daily_pnl) >= max_daily_loss_amount:
                    return False, f"当日损失 {abs(self.daily_pnl):.2f} 已达到限制 {max_daily_loss_amount:.2f}"
            
            # 检查是否已有相同股票的反向仓位
            if symbol in self.positions:
                existing_position = self.positions[symbol]
                if (existing_position.side == "long" and side == "short") or \
                   (existing_position.side == "short" and side == "long"):
                    return False, f"{symbol} 已有反向仓位，不允许开仓"
            
            return True, "风险检查通过"
            
        except Exception as e:
            logger.error(f"风险检查失败: {e}")
            return False, f"风险检查错误: {e}"
    
    def calculate_stop_loss_take_profit(self, entry_price: float, side: str, 
                                      volatility: float = None) -> Tuple[float, float]:
        """计算止损止盈价格"""
        try:
            # 基础止损止盈百分比
            stop_loss_pct = self.stop_loss_percent
            take_profit_pct = self.take_profit_percent
            
            # 基于波动率调整（如果提供）
            if volatility is not None:
                # 高波动率增加止损止盈距离
                volatility_multiplier = 1.0 + volatility
                stop_loss_pct *= volatility_multiplier
                take_profit_pct *= volatility_multiplier
            
            if side == "long":
                stop_loss = entry_price * (1 - stop_loss_pct)
                take_profit = entry_price * (1 + take_profit_pct)
            else:  # short
                stop_loss = entry_price * (1 + stop_loss_pct)
                take_profit = entry_price * (1 - take_profit_pct)
            
            return stop_loss, take_profit
            
        except Exception as e:
            logger.error(f"计算止损止盈失败: {e}")
            return entry_price * 0.98, entry_price * 1.02  # 默认值
    
    def open_position(self, symbol: str, quantity: float, price: float, side: str) -> bool:
        """开仓"""
        try:
            # 风险检查
            risk_ok, risk_msg = self.check_risk_limits(symbol, quantity, price, side)
            if not risk_ok:
                logger.warning(f"开仓被拒绝: {risk_msg}")
                return False
            
            # 计算止损止盈
            stop_loss, take_profit = self.calculate_stop_loss_take_profit(price, side)
            
            # 创建仓位
            position = Position(
                symbol=symbol,
                quantity=quantity if side == "long" else -quantity,
                entry_price=price,
                current_price=price,
                side=side,
                entry_time=datetime.now(),
                stop_loss=stop_loss,
                take_profit=take_profit
            )
            
            # 更新仓位
            if symbol in self.positions:
                # 如果已有仓位，合并
                existing = self.positions[symbol]
                total_quantity = existing.quantity + position.quantity
                if total_quantity == 0:
                    # 完全对冲，删除仓位
                    del self.positions[symbol]
                else:
                    # 更新平均成本
                    total_cost = (existing.quantity * existing.entry_price + 
                                position.quantity * position.entry_price)
                    avg_price = total_cost / total_quantity
                    
                    existing.quantity = total_quantity
                    existing.entry_price = avg_price
                    existing.side = "long" if total_quantity > 0 else "short"
            else:
                self.positions[symbol] = position
            
            # 更新资金
            trade_cost = quantity * price
            self.current_capital -= trade_cost
            
            # 记录交易
            trade_record = {
                'symbol': symbol,
                'side': side,
                'quantity': quantity,
                'price': price,
                'timestamp': datetime.now(),
                'type': 'open'
            }
            self.trade_history.append(trade_record)
            self.daily_trades += 1
            
            logger.info(f"开仓成功: {symbol} {side} {quantity}@{price:.2f}")
            return True
            
        except Exception as e:
            logger.error(f"开仓失败: {e}")
            return False
    
    def close_position(self, symbol: str, quantity: float = None, price: float = None) -> bool:
        """平仓"""
        try:
            if symbol not in self.positions:
                logger.warning(f"没有找到 {symbol} 的仓位")
                return False
            
            position = self.positions[symbol]
            
            # 如果没有指定数量，全部平仓
            if quantity is None:
                quantity = abs(position.quantity)
            
            # 如果没有指定价格，使用当前价格
            if price is None:
                price = position.current_price
            
            # 计算平仓数量
            close_quantity = min(quantity, abs(position.quantity))
            
            # 计算盈亏
            if position.side == "long":
                pnl = close_quantity * (price - position.entry_price)
            else:
                pnl = close_quantity * (position.entry_price - price)
            
            # 更新资金
            self.current_capital += close_quantity * price + pnl
            self.daily_pnl += pnl
            
            # 更新仓位
            if close_quantity >= abs(position.quantity):
                # 全部平仓
                del self.positions[symbol]
            else:
                # 部分平仓
                if position.quantity > 0:
                    position.quantity -= close_quantity
                else:
                    position.quantity += close_quantity
            
            # 记录交易
            trade_record = {
                'symbol': symbol,
                'side': 'sell' if position.side == 'long' else 'buy',
                'quantity': close_quantity,
                'price': price,
                'pnl': pnl,
                'timestamp': datetime.now(),
                'type': 'close'
            }
            self.trade_history.append(trade_record)
            self.daily_trades += 1
            
            logger.info(f"平仓成功: {symbol} {close_quantity}@{price:.2f}, 盈亏: {pnl:.2f}")
            return True
            
        except Exception as e:
            logger.error(f"平仓失败: {e}")
            return False
    
    def update_positions(self, price_data: Dict[str, float]):
        """更新仓位价格"""
        try:
            for symbol, position in self.positions.items():
                if symbol in price_data:
                    old_price = position.current_price
                    new_price = price_data[symbol]
                    position.current_price = new_price
                    
                    # 计算盈亏变化
                    if position.side == "long":
                        pnl_change = position.quantity * (new_price - old_price)
                    else:
                        pnl_change = position.quantity * (old_price - new_price)
                    
                    self.daily_pnl += pnl_change
            
            # 更新组合价值
            portfolio_value = self.get_portfolio_value()
            
            # 更新最大回撤
            if portfolio_value > self.peak_value:
                self.peak_value = portfolio_value
            
            current_drawdown = (self.peak_value - portfolio_value) / self.peak_value
            if current_drawdown > self.max_drawdown:
                self.max_drawdown = current_drawdown
                
        except Exception as e:
            logger.error(f"更新仓位失败: {e}")
    
    def check_stop_loss_take_profit(self) -> List[Dict[str, Any]]:
        """检查止损止盈"""
        try:
            signals = []
            
            for symbol, position in self.positions.items():
                current_price = position.current_price
                
                # 检查止损
                if position.stop_loss is not None:
                    if (position.side == "long" and current_price <= position.stop_loss) or \
                       (position.side == "short" and current_price >= position.stop_loss):
                        signals.append({
                            'symbol': symbol,
                            'action': 'stop_loss',
                            'price': current_price,
                            'quantity': abs(position.quantity),
                            'reason': f'触发止损: {current_price:.2f} vs {position.stop_loss:.2f}'
                        })
                
                # 检查止盈
                if position.take_profit is not None:
                    if (position.side == "long" and current_price >= position.take_profit) or \
                       (position.side == "short" and current_price <= position.take_profit):
                        signals.append({
                            'symbol': symbol,
                            'action': 'take_profit',
                            'price': current_price,
                            'quantity': abs(position.quantity),
                            'reason': f'触发止盈: {current_price:.2f} vs {position.take_profit:.2f}'
                        })
            
            return signals
            
        except Exception as e:
            logger.error(f"检查止损止盈失败: {e}")
            return []
    
    def get_portfolio_value(self) -> float:
        """获取组合总价值"""
        try:
            total_value = self.current_capital
            
            for position in self.positions.values():
                total_value += position.market_value + position.unrealized_pnl
            
            return total_value
            
        except Exception as e:
            logger.error(f"计算组合价值失败: {e}")
            return self.current_capital
    
    def get_total_exposure(self) -> float:
        """获取总仓位"""
        try:
            total_exposure = 0
            for position in self.positions.values():
                total_exposure += position.market_value
            return total_exposure
            
        except Exception as e:
            logger.error(f"计算总仓位失败: {e}")
            return 0.0
    
    def calculate_var(self, confidence_level: float = 0.95, days: int = 30) -> float:
        """计算风险价值(VaR)"""
        try:
            if len(self.daily_returns) < days:
                return 0.0
            
            recent_returns = self.daily_returns[-days:]
            portfolio_value = self.get_portfolio_value()
            
            # 计算VaR
            var_percentile = (1 - confidence_level) * 100
            var_return = np.percentile(recent_returns, var_percentile)
            var_amount = abs(var_return * portfolio_value)
            
            return var_amount
            
        except Exception as e:
            logger.error(f"计算VaR失败: {e}")
            return 0.0
    
    def get_risk_metrics(self) -> RiskMetrics:
        """获取风险指标"""
        try:
            portfolio_value = self.get_portfolio_value()
            total_exposure = self.get_total_exposure()
            current_drawdown = (self.peak_value - portfolio_value) / self.peak_value if self.peak_value > 0 else 0
            var_95 = self.calculate_var()
            
            # 计算夏普比率
            if len(self.daily_returns) > 1:
                returns_array = np.array(self.daily_returns)
                sharpe_ratio = np.mean(returns_array) / np.std(returns_array) * np.sqrt(252) if np.std(returns_array) > 0 else 0
            else:
                sharpe_ratio = 0
            
            # 确定风险等级
            risk_level = RiskLevel.LOW
            if current_drawdown > 0.1 or total_exposure / portfolio_value > 0.8:
                risk_level = RiskLevel.HIGH
            elif current_drawdown > 0.05 or total_exposure / portfolio_value > 0.6:
                risk_level = RiskLevel.MEDIUM
            
            if abs(self.daily_pnl) > self.current_capital * self.max_daily_loss:
                risk_level = RiskLevel.CRITICAL
            
            return RiskMetrics(
                total_exposure=total_exposure,
                max_position_size=self.max_position_size,
                current_drawdown=current_drawdown,
                max_drawdown=self.max_drawdown,
                var_95=var_95,
                sharpe_ratio=sharpe_ratio,
                risk_level=risk_level,
                daily_pnl=self.daily_pnl,
                portfolio_value=portfolio_value
            )
            
        except Exception as e:
            logger.error(f"获取风险指标失败: {e}")
            return RiskMetrics(0, 0, 0, 0, 0, 0, RiskLevel.LOW, 0, self.current_capital)
    
    def reset_daily_stats(self):
        """重置当日统计"""
        try:
            # 计算当日收益率
            portfolio_value = self.get_portfolio_value()
            daily_return = (portfolio_value - self.daily_start_capital) / self.daily_start_capital
            self.daily_returns.append(daily_return)
            
            # 保持最近252个交易日的数据
            if len(self.daily_returns) > 252:
                self.daily_returns.pop(0)
            
            # 重置当日统计
            self.daily_pnl = 0.0
            self.daily_trades = 0
            self.daily_start_capital = portfolio_value
            
            logger.info(f"重置当日统计，当日收益率: {daily_return:.4f}")
            
        except Exception as e:
            logger.error(f"重置当日统计失败: {e}")


# 全局风险管理器实例
risk_manager = RiskManager()

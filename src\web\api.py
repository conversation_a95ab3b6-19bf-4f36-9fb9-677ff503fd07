"""
FastAPI后端服务 - 提供REST API接口
"""
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import asyncio
import uvicorn
from loguru import logger

from src.trading.strategy_engine import strategy_engine
from src.trading.risk_manager import risk_manager
from src.trading.execution_engine import execution_engine
from src.models.model_manager import model_manager
from src.data.database import db_manager
from src.backtesting.backtest_engine import backtest_engine
from config.settings import settings


# Pydantic模型
class TradingSignalRequest(BaseModel):
    symbol: str
    action: str
    confidence: float
    price: float
    model_name: Optional[str] = None


class BacktestRequest(BaseModel):
    symbol: str
    start_date: datetime
    end_date: datetime
    model_type: str = "random_forest"
    initial_capital: float = 100000


class ModelTrainRequest(BaseModel):
    symbol: str
    model_type: str
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None


class StrategySettings(BaseModel):
    min_confidence: float
    prediction_interval: int
    symbols: List[str]


# 创建FastAPI应用
app = FastAPI(
    title="美股短线交易AI系统API",
    description="提供交易策略、风险管理和模型预测的API接口",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/")
async def root():
    """根路径"""
    return {"message": "美股短线交易AI系统API", "version": "1.0.0"}


@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now(),
        "strategy_running": strategy_engine.is_running
    }


# 策略引擎相关接口
@app.post("/strategy/start")
async def start_strategy(background_tasks: BackgroundTasks):
    """启动策略引擎"""
    try:
        if strategy_engine.is_running:
            raise HTTPException(status_code=400, detail="策略引擎已在运行")
        
        # 在后台任务中启动策略
        background_tasks.add_task(strategy_engine.start)
        
        return {"message": "策略引擎启动中", "status": "starting"}
        
    except Exception as e:
        logger.error(f"启动策略引擎失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/strategy/stop")
async def stop_strategy():
    """停止策略引擎"""
    try:
        strategy_engine.stop()
        return {"message": "策略引擎已停止", "status": "stopped"}
        
    except Exception as e:
        logger.error(f"停止策略引擎失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/strategy/status")
async def get_strategy_status():
    """获取策略状态"""
    try:
        status = strategy_engine.get_strategy_status()
        return status
        
    except Exception as e:
        logger.error(f"获取策略状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.put("/strategy/settings")
async def update_strategy_settings(settings: StrategySettings):
    """更新策略设置"""
    try:
        strategy_engine.set_min_confidence(settings.min_confidence)
        strategy_engine.set_prediction_interval(settings.prediction_interval)
        
        # 更新监控股票
        current_symbols = set(strategy_engine.symbols)
        new_symbols = set(settings.symbols)
        
        # 添加新股票
        for symbol in new_symbols - current_symbols:
            strategy_engine.add_symbol(symbol)
        
        # 移除不需要的股票
        for symbol in current_symbols - new_symbols:
            strategy_engine.remove_symbol(symbol)
        
        return {"message": "策略设置已更新"}
        
    except Exception as e:
        logger.error(f"更新策略设置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 风险管理相关接口
@app.get("/risk/metrics")
async def get_risk_metrics():
    """获取风险指标"""
    try:
        metrics = risk_manager.get_risk_metrics()
        return {
            "total_exposure": metrics.total_exposure,
            "max_position_size": metrics.max_position_size,
            "current_drawdown": metrics.current_drawdown,
            "max_drawdown": metrics.max_drawdown,
            "var_95": metrics.var_95,
            "sharpe_ratio": metrics.sharpe_ratio,
            "risk_level": metrics.risk_level.value,
            "daily_pnl": metrics.daily_pnl,
            "portfolio_value": metrics.portfolio_value
        }
        
    except Exception as e:
        logger.error(f"获取风险指标失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/risk/positions")
async def get_positions():
    """获取当前持仓"""
    try:
        positions = []
        for symbol, position in risk_manager.positions.items():
            positions.append({
                "symbol": symbol,
                "quantity": position.quantity,
                "entry_price": position.entry_price,
                "current_price": position.current_price,
                "side": position.side,
                "market_value": position.market_value,
                "unrealized_pnl": position.unrealized_pnl,
                "unrealized_pnl_percent": position.unrealized_pnl_percent,
                "entry_time": position.entry_time,
                "stop_loss": position.stop_loss,
                "take_profit": position.take_profit
            })
        
        return {"positions": positions}
        
    except Exception as e:
        logger.error(f"获取持仓失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 交易执行相关接口
@app.post("/trading/signal")
async def submit_trading_signal(signal: TradingSignalRequest):
    """提交交易信号"""
    try:
        from src.trading.execution_engine import TradingSignal
        
        trading_signal = TradingSignal(
            symbol=signal.symbol,
            action=signal.action,
            confidence=signal.confidence,
            price=signal.price,
            model_name=signal.model_name
        )
        
        order = execution_engine.process_trading_signal(trading_signal)
        
        if order:
            return {
                "message": "交易信号已处理",
                "order_id": order.id,
                "status": order.status.value
            }
        else:
            raise HTTPException(status_code=400, detail="交易信号处理失败")
            
    except Exception as e:
        logger.error(f"提交交易信号失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/trading/orders")
async def get_orders(symbol: Optional[str] = None, limit: int = 100):
    """获取订单列表"""
    try:
        orders = execution_engine.get_orders(symbol=symbol)
        
        order_list = []
        for order in orders[-limit:]:
            order_list.append({
                "id": order.id,
                "symbol": order.symbol,
                "side": order.side.value,
                "order_type": order.order_type.value,
                "quantity": order.quantity,
                "price": order.price,
                "status": order.status.value,
                "filled_quantity": order.filled_quantity,
                "filled_price": order.filled_price,
                "created_at": order.created_at,
                "updated_at": order.updated_at
            })
        
        return {"orders": order_list}
        
    except Exception as e:
        logger.error(f"获取订单列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/trading/stats")
async def get_execution_stats():
    """获取交易统计"""
    try:
        stats = execution_engine.get_execution_stats()
        return stats
        
    except Exception as e:
        logger.error(f"获取交易统计失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 模型相关接口
@app.post("/models/train")
async def train_model(request: ModelTrainRequest, background_tasks: BackgroundTasks):
    """训练模型"""
    try:
        # 获取历史数据
        end_date = request.end_date or datetime.now()
        start_date = request.start_date or (end_date - timedelta(days=90))
        
        df = db_manager.query_market_data(request.symbol, start_date, end_date)
        
        if df.empty:
            raise HTTPException(status_code=400, detail="没有足够的历史数据")
        
        # 在后台任务中训练模型
        if request.model_type == "lstm":
            background_tasks.add_task(
                model_manager.train_lstm_model,
                request.symbol,
                df
            )
        else:
            background_tasks.add_task(
                model_manager.train_ml_model,
                request.symbol,
                df,
                request.model_type
            )
        
        return {"message": f"模型训练已开始", "symbol": request.symbol, "model_type": request.model_type}
        
    except Exception as e:
        logger.error(f"训练模型失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/models/list")
async def list_models():
    """获取模型列表"""
    try:
        models = model_manager.list_models()
        return {"models": models}
        
    except Exception as e:
        logger.error(f"获取模型列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/models/predict/{symbol}")
async def predict(symbol: str, features: List[float]):
    """模型预测"""
    try:
        import numpy as np
        
        features_array = np.array(features)
        predictions = model_manager.predict(symbol, features_array)
        
        return {"predictions": predictions}
        
    except Exception as e:
        logger.error(f"模型预测失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 回测相关接口
@app.post("/backtest/run")
async def run_backtest(request: BacktestRequest, background_tasks: BackgroundTasks):
    """运行回测"""
    try:
        # 在后台任务中运行回测
        def run_backtest_task():
            try:
                result = backtest_engine.run_backtest(
                    symbol=request.symbol,
                    start_date=request.start_date,
                    end_date=request.end_date,
                    model_config={'model_type': request.model_type}
                )
                
                # 保存回测结果到缓存
                cache_key = f"backtest_{request.symbol}_{datetime.now().timestamp()}"
                db_manager.cache_set(cache_key, {
                    "total_return": result.total_return,
                    "annual_return": result.annual_return,
                    "sharpe_ratio": result.sharpe_ratio,
                    "max_drawdown": result.max_drawdown,
                    "win_rate": result.win_rate,
                    "total_trades": result.total_trades
                }, expire=3600)
                
                logger.info(f"回测完成: {request.symbol}")
                
            except Exception as e:
                logger.error(f"回测任务失败: {e}")
        
        background_tasks.add_task(run_backtest_task)
        
        return {"message": "回测已开始", "symbol": request.symbol}
        
    except Exception as e:
        logger.error(f"启动回测失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 数据相关接口
@app.get("/data/signals")
async def get_trading_signals(symbol: Optional[str] = None, limit: int = 50):
    """获取交易信号历史"""
    try:
        signals = db_manager.get_trading_signals(symbol=symbol, limit=limit)
        return {"signals": signals}
        
    except Exception as e:
        logger.error(f"获取交易信号失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/data/trades")
async def get_trades(symbol: Optional[str] = None, limit: int = 50):
    """获取交易记录"""
    try:
        trades = db_manager.get_trades(symbol=symbol, limit=limit)
        return {"trades": trades}
        
    except Exception as e:
        logger.error(f"获取交易记录失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/data/market/{symbol}")
async def get_market_data(symbol: str, days: int = 7):
    """获取市场数据"""
    try:
        end_time = datetime.now()
        start_time = end_time - timedelta(days=days)
        
        df = db_manager.query_market_data(symbol, start_time, end_time)
        
        if df.empty:
            return {"data": []}
        
        # 转换为JSON格式
        data = []
        for timestamp, row in df.iterrows():
            data.append({
                "timestamp": timestamp.isoformat(),
                "open": row.get("open"),
                "high": row.get("high"),
                "low": row.get("low"),
                "close": row.get("close"),
                "volume": row.get("volume")
            })
        
        return {"data": data}
        
    except Exception as e:
        logger.error(f"获取市场数据失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


if __name__ == "__main__":
    uvicorn.run(
        "src.web.api:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )

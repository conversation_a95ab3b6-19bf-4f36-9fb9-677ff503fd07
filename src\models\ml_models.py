"""
传统机器学习模型 - 随机森林、XGBoost、SVM等
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime
import joblib
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.svm import SVC
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import GridSearchCV, cross_val_score
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, classification_report
import xgboost as xgb
from loguru import logger


class MLPredictor:
    """机器学习预测器基类"""
    
    def __init__(self, model_type: str = "random_forest"):
        self.model_type = model_type
        self.model = None
        self.scaler = StandardScaler()
        self.label_encoder = LabelEncoder()
        self.is_trained = False
        self.feature_names = None
        self.feature_importance_ = None
        
        # 初始化模型
        self._initialize_model()
    
    def _initialize_model(self):
        """初始化模型"""
        if self.model_type == "random_forest":
            self.model = RandomForestClassifier(
                n_estimators=200,
                max_depth=15,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42,
                n_jobs=-1
            )
        elif self.model_type == "xgboost":
            self.model = xgb.XGBClassifier(
                n_estimators=200,
                max_depth=6,
                learning_rate=0.1,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=42,
                n_jobs=-1
            )
        elif self.model_type == "gradient_boosting":
            self.model = GradientBoostingClassifier(
                n_estimators=200,
                max_depth=6,
                learning_rate=0.1,
                subsample=0.8,
                random_state=42
            )
        elif self.model_type == "svm":
            self.model = SVC(
                kernel='rbf',
                C=1.0,
                gamma='scale',
                probability=True,
                random_state=42
            )
        elif self.model_type == "logistic_regression":
            self.model = LogisticRegression(
                C=1.0,
                max_iter=1000,
                random_state=42,
                n_jobs=-1
            )
        else:
            raise ValueError(f"不支持的模型类型: {self.model_type}")
    
    def prepare_features(self, df: pd.DataFrame, target_column: str = 'target_direction') -> Tuple[np.ndarray, np.ndarray]:
        """准备特征数据"""
        try:
            # 选择数值特征列
            numeric_columns = df.select_dtypes(include=[np.number]).columns.tolist()
            
            # 排除目标列和时间相关列
            exclude_columns = [target_column, 'target_return', 'target_binary']
            feature_columns = [col for col in numeric_columns if col not in exclude_columns]
            
            # 移除包含NaN的列
            feature_columns = [col for col in feature_columns if not df[col].isna().all()]
            
            self.feature_names = feature_columns
            
            # 提取特征和目标
            X = df[feature_columns].fillna(0).values
            y = df[target_column].values
            
            logger.info(f"特征准备完成: {len(feature_columns)} 个特征, {len(X)} 个样本")
            return X, y
            
        except Exception as e:
            logger.error(f"特征准备失败: {e}")
            raise
    
    def train(self, X_train: np.ndarray, y_train: np.ndarray, 
              optimize_hyperparameters: bool = False) -> Dict[str, Any]:
        """训练模型"""
        try:
            # 数据标准化
            X_train_scaled = self.scaler.fit_transform(X_train)
            
            # 标签编码
            y_train_encoded = self.label_encoder.fit_transform(y_train)
            
            if optimize_hyperparameters:
                # 超参数优化
                self._optimize_hyperparameters(X_train_scaled, y_train_encoded)
            
            # 训练模型
            self.model.fit(X_train_scaled, y_train_encoded)
            self.is_trained = True
            
            # 获取特征重要性
            if hasattr(self.model, 'feature_importances_'):
                self.feature_importance_ = self.model.feature_importances_
            
            # 交叉验证评估
            cv_scores = cross_val_score(self.model, X_train_scaled, y_train_encoded, cv=5)
            
            results = {
                'model_type': self.model_type,
                'cv_mean_accuracy': np.mean(cv_scores),
                'cv_std_accuracy': np.std(cv_scores),
                'training_samples': len(X_train),
                'features_count': X_train.shape[1]
            }
            
            logger.info(f"{self.model_type} 模型训练完成: CV准确率 {results['cv_mean_accuracy']:.4f} ± {results['cv_std_accuracy']:.4f}")
            return results
            
        except Exception as e:
            logger.error(f"模型训练失败: {e}")
            raise
    
    def _optimize_hyperparameters(self, X: np.ndarray, y: np.ndarray):
        """超参数优化"""
        try:
            param_grids = {
                "random_forest": {
                    'n_estimators': [100, 200, 300],
                    'max_depth': [10, 15, 20, None],
                    'min_samples_split': [2, 5, 10],
                    'min_samples_leaf': [1, 2, 4]
                },
                "xgboost": {
                    'n_estimators': [100, 200, 300],
                    'max_depth': [3, 6, 9],
                    'learning_rate': [0.01, 0.1, 0.2],
                    'subsample': [0.8, 0.9, 1.0]
                },
                "gradient_boosting": {
                    'n_estimators': [100, 200, 300],
                    'max_depth': [3, 6, 9],
                    'learning_rate': [0.01, 0.1, 0.2],
                    'subsample': [0.8, 0.9, 1.0]
                },
                "svm": {
                    'C': [0.1, 1, 10, 100],
                    'gamma': ['scale', 'auto', 0.001, 0.01, 0.1, 1],
                    'kernel': ['rbf', 'poly']
                },
                "logistic_regression": {
                    'C': [0.01, 0.1, 1, 10, 100],
                    'penalty': ['l1', 'l2'],
                    'solver': ['liblinear', 'saga']
                }
            }
            
            if self.model_type in param_grids:
                param_grid = param_grids[self.model_type]
                
                grid_search = GridSearchCV(
                    self.model,
                    param_grid,
                    cv=3,
                    scoring='accuracy',
                    n_jobs=-1,
                    verbose=1
                )
                
                grid_search.fit(X, y)
                self.model = grid_search.best_estimator_
                
                logger.info(f"最佳参数: {grid_search.best_params_}")
                logger.info(f"最佳CV分数: {grid_search.best_score_:.4f}")
            
        except Exception as e:
            logger.error(f"超参数优化失败: {e}")
    
    def predict(self, X: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """预测"""
        try:
            if not self.is_trained:
                raise ValueError("模型尚未训练")
            
            # 数据标准化
            X_scaled = self.scaler.transform(X)
            
            # 预测
            predictions_encoded = self.model.predict(X_scaled)
            probabilities = self.model.predict_proba(X_scaled)
            
            # 解码预测结果
            predictions = self.label_encoder.inverse_transform(predictions_encoded)
            
            # 获取置信度（最大概率）
            confidence = np.max(probabilities, axis=1)
            
            return predictions, confidence
            
        except Exception as e:
            logger.error(f"预测失败: {e}")
            raise
    
    def predict_single(self, features: np.ndarray) -> Tuple[int, float]:
        """单个样本预测"""
        try:
            if features.ndim == 1:
                features = features.reshape(1, -1)
            
            predictions, confidence = self.predict(features)
            return int(predictions[0]), float(confidence[0])
            
        except Exception as e:
            logger.error(f"单个预测失败: {e}")
            raise
    
    def evaluate(self, X_test: np.ndarray, y_test: np.ndarray) -> Dict[str, float]:
        """评估模型性能"""
        try:
            if not self.is_trained:
                raise ValueError("模型尚未训练")
            
            predictions, confidence = self.predict(X_test)
            
            # 计算指标
            accuracy = accuracy_score(y_test, predictions)
            precision = precision_score(y_test, predictions, average='weighted', zero_division=0)
            recall = recall_score(y_test, predictions, average='weighted', zero_division=0)
            f1 = f1_score(y_test, predictions, average='weighted', zero_division=0)
            
            # 计算每个类别的准确率
            class_accuracies = {}
            for class_label in np.unique(y_test):
                mask = y_test == class_label
                if np.sum(mask) > 0:
                    class_acc = accuracy_score(y_test[mask], predictions[mask])
                    class_accuracies[f'accuracy_class_{class_label}'] = class_acc
            
            results = {
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1_score': f1,
                'mean_confidence': np.mean(confidence),
                **class_accuracies
            }
            
            logger.info(f"{self.model_type} 模型评估完成: {results}")
            return results
            
        except Exception as e:
            logger.error(f"模型评估失败: {e}")
            raise
    
    def get_feature_importance(self) -> Dict[str, float]:
        """获取特征重要性"""
        try:
            if not self.is_trained or self.feature_importance_ is None:
                return {}
            
            if self.feature_names is None:
                return {}
            
            importance_dict = {}
            for i, importance in enumerate(self.feature_importance_):
                if i < len(self.feature_names):
                    importance_dict[self.feature_names[i]] = float(importance)
            
            # 按重要性排序
            importance_dict = dict(sorted(importance_dict.items(), key=lambda x: x[1], reverse=True))
            
            return importance_dict
            
        except Exception as e:
            logger.error(f"获取特征重要性失败: {e}")
            return {}
    
    def save_model(self, filepath: str):
        """保存模型"""
        try:
            if not self.is_trained:
                raise ValueError("没有可保存的模型")
            
            model_data = {
                'model': self.model,
                'scaler': self.scaler,
                'label_encoder': self.label_encoder,
                'model_type': self.model_type,
                'feature_names': self.feature_names,
                'feature_importance_': self.feature_importance_,
                'is_trained': self.is_trained
            }
            
            joblib.dump(model_data, filepath)
            logger.info(f"模型已保存到: {filepath}")
            
        except Exception as e:
            logger.error(f"模型保存失败: {e}")
            raise
    
    def load_model(self, filepath: str):
        """加载模型"""
        try:
            model_data = joblib.load(filepath)
            
            self.model = model_data['model']
            self.scaler = model_data['scaler']
            self.label_encoder = model_data['label_encoder']
            self.model_type = model_data['model_type']
            self.feature_names = model_data['feature_names']
            self.feature_importance_ = model_data.get('feature_importance_')
            self.is_trained = model_data['is_trained']
            
            logger.info(f"模型已从 {filepath} 加载")
            
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise


class MLEnsemble:
    """机器学习集成模型"""
    
    def __init__(self, model_types: List[str] = None):
        self.model_types = model_types or ["random_forest", "xgboost", "gradient_boosting"]
        self.models = {}
        self.is_trained = False
        self.weights = None
    
    def train(self, X_train: np.ndarray, y_train: np.ndarray, 
              X_val: np.ndarray = None, y_val: np.ndarray = None) -> Dict[str, Any]:
        """训练集成模型"""
        try:
            results = {}
            
            for model_type in self.model_types:
                logger.info(f"训练 {model_type} 模型")
                
                model = MLPredictor(model_type=model_type)
                train_result = model.train(X_train, y_train)
                
                self.models[model_type] = model
                results[model_type] = train_result
            
            # 如果有验证集，计算权重
            if X_val is not None and y_val is not None:
                self._calculate_weights(X_val, y_val)
            else:
                # 等权重
                self.weights = {model_type: 1.0 / len(self.model_types) for model_type in self.model_types}
            
            self.is_trained = True
            logger.info("集成模型训练完成")
            return results
            
        except Exception as e:
            logger.error(f"集成模型训练失败: {e}")
            raise
    
    def _calculate_weights(self, X_val: np.ndarray, y_val: np.ndarray):
        """基于验证集性能计算模型权重"""
        try:
            accuracies = {}
            
            for model_type, model in self.models.items():
                eval_results = model.evaluate(X_val, y_val)
                accuracies[model_type] = eval_results['accuracy']
            
            # 基于准确率计算权重
            total_accuracy = sum(accuracies.values())
            self.weights = {model_type: acc / total_accuracy for model_type, acc in accuracies.items()}
            
            logger.info(f"模型权重: {self.weights}")
            
        except Exception as e:
            logger.error(f"权重计算失败: {e}")
            # 使用等权重
            self.weights = {model_type: 1.0 / len(self.model_types) for model_type in self.model_types}
    
    def predict(self, X: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """集成预测"""
        try:
            if not self.is_trained:
                raise ValueError("集成模型尚未训练")
            
            all_predictions = {}
            all_confidences = {}
            
            for model_type, model in self.models.items():
                pred, conf = model.predict(X)
                all_predictions[model_type] = pred
                all_confidences[model_type] = conf
            
            # 加权投票
            final_predictions = []
            final_confidences = []
            
            for i in range(len(X)):
                # 收集所有模型的预测
                votes = {}
                total_confidence = 0
                
                for model_type in self.model_types:
                    pred = all_predictions[model_type][i]
                    conf = all_confidences[model_type][i]
                    weight = self.weights[model_type]
                    
                    if pred not in votes:
                        votes[pred] = 0
                    votes[pred] += weight * conf
                    total_confidence += weight * conf
                
                # 选择得票最高的预测
                final_pred = max(votes.keys(), key=lambda k: votes[k])
                final_conf = total_confidence / len(self.model_types)
                
                final_predictions.append(final_pred)
                final_confidences.append(final_conf)
            
            return np.array(final_predictions), np.array(final_confidences)
            
        except Exception as e:
            logger.error(f"集成预测失败: {e}")
            raise

"""
美股短线交易AI软件配置文件
"""
import os
from typing import Dict, Any
from pydantic import BaseSettings, Field


class Settings(BaseSettings):
    """应用配置类"""
    
    # 应用基础配置
    APP_NAME: str = "美股短线交易AI"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = Field(default=False, env="DEBUG")
    
    # API配置
    POLYGON_API_KEY: str = Field(..., env="POLYGON_API_KEY")
    ALPHA_VANTAGE_API_KEY: str = Field(..., env="ALPHA_VANTAGE_API_KEY")
    ALPACA_API_KEY: str = Field(..., env="ALPACA_API_KEY")
    ALPACA_SECRET_KEY: str = Field(..., env="ALPACA_SECRET_KEY")
    ALPACA_BASE_URL: str = Field(default="https://paper-api.alpaca.markets", env="ALPACA_BASE_URL")
    
    # 数据库配置
    POSTGRES_HOST: str = Field(default="localhost", env="POSTGRES_HOST")
    POSTGRES_PORT: int = Field(default=5432, env="POSTGRES_PORT")
    POSTGRES_DB: str = Field(default="trading_ai", env="POSTGRES_DB")
    POSTGRES_USER: str = Field(default="postgres", env="POSTGRES_USER")
    POSTGRES_PASSWORD: str = Field(..., env="POSTGRES_PASSWORD")
    
    # Redis配置
    REDIS_HOST: str = Field(default="localhost", env="REDIS_HOST")
    REDIS_PORT: int = Field(default=6379, env="REDIS_PORT")
    REDIS_DB: int = Field(default=0, env="REDIS_DB")
    
    # InfluxDB配置
    INFLUXDB_URL: str = Field(default="http://localhost:8086", env="INFLUXDB_URL")
    INFLUXDB_TOKEN: str = Field(..., env="INFLUXDB_TOKEN")
    INFLUXDB_ORG: str = Field(default="trading", env="INFLUXDB_ORG")
    INFLUXDB_BUCKET: str = Field(default="market_data", env="INFLUXDB_BUCKET")
    
    # 交易配置
    MAX_POSITION_SIZE: float = Field(default=0.02, description="单笔交易最大仓位比例")
    MAX_DAILY_LOSS: float = Field(default=0.05, description="日最大损失比例")
    STOP_LOSS_PERCENT: float = Field(default=0.02, description="止损百分比")
    TAKE_PROFIT_PERCENT: float = Field(default=0.04, description="止盈百分比")
    
    # AI模型配置
    MODEL_UPDATE_INTERVAL: int = Field(default=3600, description="模型更新间隔(秒)")
    PREDICTION_CONFIDENCE_THRESHOLD: float = Field(default=0.7, description="预测置信度阈值")
    
    # 监控配置
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    METRICS_PORT: int = Field(default=8000, env="METRICS_PORT")
    
    @property
    def database_url(self) -> str:
        """获取数据库连接URL"""
        return f"postgresql://{self.POSTGRES_USER}:{self.POSTGRES_PASSWORD}@{self.POSTGRES_HOST}:{self.POSTGRES_PORT}/{self.POSTGRES_DB}"
    
    @property
    def redis_url(self) -> str:
        """获取Redis连接URL"""
        return f"redis://{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# 全局配置实例
settings = Settings()


# 交易时间配置
TRADING_HOURS = {
    "market_open": "09:30",
    "market_close": "16:00",
    "pre_market_start": "04:00",
    "after_hours_end": "20:00"
}

# 支持的股票列表
SUPPORTED_SYMBOLS = [
    "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "META", "NVDA", "NFLX",
    "SPY", "QQQ", "IWM", "DIA", "VIX", "GLD", "SLV", "TLT"
]

# 技术指标配置
TECHNICAL_INDICATORS = {
    "sma_periods": [5, 10, 20, 50],
    "ema_periods": [12, 26],
    "rsi_period": 14,
    "macd_fast": 12,
    "macd_slow": 26,
    "macd_signal": 9,
    "bollinger_period": 20,
    "bollinger_std": 2
}

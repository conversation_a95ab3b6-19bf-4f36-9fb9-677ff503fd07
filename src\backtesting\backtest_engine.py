"""
回测引擎 - 历史数据策略验证和性能评估
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
import matplotlib.pyplot as plt
import seaborn as sns
from loguru import logger

from src.data.data_processor import data_processor
from src.models.model_manager import model_manager
from src.trading.risk_manager import RiskManager, Position


@dataclass
class BacktestResult:
    """回测结果"""
    total_return: float
    annual_return: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    profit_factor: float
    total_trades: int
    avg_trade_return: float
    volatility: float
    calmar_ratio: float
    equity_curve: pd.Series
    trades: List[Dict]
    daily_returns: pd.Series


class BacktestEngine:
    """回测引擎"""
    
    def __init__(self, initial_capital: float = 100000):
        self.initial_capital = initial_capital
        self.commission = 0.001  # 0.1% 手续费
        self.slippage = 0.0005   # 0.05% 滑点
        
    def run_backtest(self, symbol: str, start_date: datetime, end_date: datetime,
                    model_config: Dict[str, Any] = None) -> BacktestResult:
        """运行回测"""
        try:
            logger.info(f"开始回测: {symbol} {start_date} - {end_date}")
            
            # 获取历史数据
            df = self._get_historical_data(symbol, start_date, end_date)
            if df.empty:
                raise ValueError("没有获取到历史数据")
            
            # 数据预处理
            processed_df = self._prepare_data(df)
            
            # 训练模型（使用前70%数据）
            train_size = int(len(processed_df) * 0.7)
            train_df = processed_df.iloc[:train_size]
            test_df = processed_df.iloc[train_size:]
            
            model = self._train_model(symbol, train_df, model_config)
            
            # 运行回测
            result = self._simulate_trading(symbol, test_df, model)
            
            logger.info(f"回测完成: 总收益率 {result.total_return:.2%}, 夏普比率 {result.sharpe_ratio:.2f}")
            return result
            
        except Exception as e:
            logger.error(f"回测失败: {e}")
            raise
    
    def _get_historical_data(self, symbol: str, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        """获取历史数据"""
        try:
            # 这里应该从数据库或API获取历史数据
            # 为了演示，我们创建模拟数据
            dates = pd.date_range(start_date, end_date, freq='1min')
            
            # 生成模拟价格数据
            np.random.seed(42)
            returns = np.random.normal(0.0001, 0.02, len(dates))
            prices = 100 * np.exp(np.cumsum(returns))
            
            # 生成OHLCV数据
            df = pd.DataFrame(index=dates)
            df['close'] = prices
            df['open'] = df['close'].shift(1).fillna(df['close'].iloc[0])
            df['high'] = np.maximum(df['open'], df['close']) * (1 + np.random.uniform(0, 0.01, len(df)))
            df['low'] = np.minimum(df['open'], df['close']) * (1 - np.random.uniform(0, 0.01, len(df)))
            df['volume'] = np.random.randint(1000, 10000, len(df))
            
            return df
            
        except Exception as e:
            logger.error(f"获取历史数据失败: {e}")
            return pd.DataFrame()
    
    def _prepare_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """准备数据"""
        try:
            # 数据清洗和特征工程
            processed_df = data_processor.clean_data(df)
            processed_df = data_processor.calculate_technical_indicators(processed_df)
            processed_df = data_processor.calculate_price_features(processed_df)
            processed_df = data_processor.create_features_for_ml(processed_df)
            processed_df = data_processor.create_target_variable(processed_df)
            
            # 删除NaN行
            processed_df = processed_df.dropna()
            
            return processed_df
            
        except Exception as e:
            logger.error(f"数据准备失败: {e}")
            return df
    
    def _train_model(self, symbol: str, train_df: pd.DataFrame, model_config: Dict[str, Any] = None) -> Any:
        """训练模型"""
        try:
            if model_config is None:
                model_config = {'model_type': 'random_forest'}
            
            model_type = model_config.get('model_type', 'random_forest')
            
            # 训练模型
            if model_type == 'lstm':
                result = model_manager.train_lstm_model(symbol, train_df)
            else:
                result = model_manager.train_ml_model(symbol, train_df, model_type)
            
            return result
            
        except Exception as e:
            logger.error(f"模型训练失败: {e}")
            return None
    
    def _simulate_trading(self, symbol: str, test_df: pd.DataFrame, model_result: Dict) -> BacktestResult:
        """模拟交易"""
        try:
            # 初始化回测环境
            capital = self.initial_capital
            positions = {}
            trades = []
            equity_curve = []
            daily_returns = []
            
            # 风险管理器
            risk_mgr = RiskManager(self.initial_capital)
            
            # 遍历测试数据
            for i, (timestamp, row) in enumerate(test_df.iterrows()):
                try:
                    current_price = row['close']
                    
                    # 获取特征
                    features = row.drop(['target_direction', 'target_return', 'target_binary']).values
                    
                    # 模型预测
                    predictions = model_manager.predict(symbol, features)
                    
                    if predictions:
                        # 获取最佳预测
                        best_pred = self._get_best_prediction(predictions)
                        
                        if best_pred:
                            prediction = best_pred.get('prediction', 0)
                            confidence = best_pred.get('confidence', 0)
                            
                            # 生成交易信号
                            signal = self._generate_signal(prediction, confidence, current_price)
                            
                            if signal:
                                # 执行交易
                                trade_result = self._execute_trade(
                                    symbol, signal, current_price, timestamp, risk_mgr
                                )
                                
                                if trade_result:
                                    trades.append(trade_result)
                    
                    # 更新仓位价格
                    risk_mgr.update_positions({symbol: current_price})
                    
                    # 检查止损止盈
                    stop_signals = risk_mgr.check_stop_loss_take_profit()
                    for stop_signal in stop_signals:
                        trade_result = self._execute_stop_trade(
                            stop_signal, timestamp, risk_mgr
                        )
                        if trade_result:
                            trades.append(trade_result)
                    
                    # 记录权益曲线
                    portfolio_value = risk_mgr.get_portfolio_value()
                    equity_curve.append(portfolio_value)
                    
                    # 计算日收益率
                    if i > 0:
                        daily_return = (portfolio_value - equity_curve[-2]) / equity_curve[-2]
                        daily_returns.append(daily_return)
                    
                except Exception as e:
                    logger.error(f"模拟交易步骤失败: {e}")
                    continue
            
            # 计算回测结果
            result = self._calculate_results(
                equity_curve, daily_returns, trades, test_df.index
            )
            
            return result
            
        except Exception as e:
            logger.error(f"模拟交易失败: {e}")
            raise
    
    def _get_best_prediction(self, predictions: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """获取最佳预测"""
        try:
            # 优先使用集成预测
            if 'ensemble' in predictions:
                return predictions['ensemble']
            
            # 否则选择置信度最高的预测
            best_pred = None
            best_confidence = 0
            
            for model_type, pred in predictions.items():
                confidence = pred.get('confidence', 0)
                if confidence > best_confidence:
                    best_confidence = confidence
                    best_pred = pred
            
            return best_pred
            
        except Exception as e:
            logger.error(f"获取最佳预测失败: {e}")
            return None
    
    def _generate_signal(self, prediction: int, confidence: float, price: float) -> Optional[Dict[str, Any]]:
        """生成交易信号"""
        try:
            # 置信度阈值
            min_confidence = 0.6
            
            if confidence < min_confidence:
                return None
            
            if prediction == 1:  # 上涨
                return {
                    'action': 'BUY',
                    'confidence': confidence,
                    'price': price
                }
            elif prediction == -1:  # 下跌
                return {
                    'action': 'SELL',
                    'confidence': confidence,
                    'price': price
                }
            
            return None
            
        except Exception as e:
            logger.error(f"生成交易信号失败: {e}")
            return None
    
    def _execute_trade(self, symbol: str, signal: Dict[str, Any], price: float, 
                      timestamp: datetime, risk_mgr: RiskManager) -> Optional[Dict[str, Any]]:
        """执行交易"""
        try:
            action = signal['action']
            confidence = signal['confidence']
            
            # 计算仓位大小
            quantity = risk_mgr.calculate_position_size(symbol, price, confidence)
            
            if quantity <= 0:
                return None
            
            # 应用滑点和手续费
            if action == 'BUY':
                execution_price = price * (1 + self.slippage)
                side = 'long'
            else:
                execution_price = price * (1 - self.slippage)
                side = 'short'
            
            # 检查风险限制
            risk_ok, risk_msg = risk_mgr.check_risk_limits(symbol, quantity, execution_price, side)
            
            if not risk_ok:
                return None
            
            # 执行交易
            if action == 'BUY':
                success = risk_mgr.open_position(symbol, quantity, execution_price, side)
            else:
                success = risk_mgr.close_position(symbol, quantity, execution_price)
            
            if success:
                # 计算手续费
                commission_cost = quantity * execution_price * self.commission
                risk_mgr.current_capital -= commission_cost
                
                return {
                    'symbol': symbol,
                    'action': action,
                    'quantity': quantity,
                    'price': execution_price,
                    'commission': commission_cost,
                    'timestamp': timestamp,
                    'confidence': confidence
                }
            
            return None
            
        except Exception as e:
            logger.error(f"执行交易失败: {e}")
            return None
    
    def _execute_stop_trade(self, stop_signal: Dict[str, Any], timestamp: datetime, 
                           risk_mgr: RiskManager) -> Optional[Dict[str, Any]]:
        """执行止损止盈交易"""
        try:
            symbol = stop_signal['symbol']
            action = stop_signal['action']
            price = stop_signal['price']
            quantity = stop_signal['quantity']
            
            # 应用滑点
            execution_price = price * (1 - self.slippage)
            
            # 执行平仓
            success = risk_mgr.close_position(symbol, quantity, execution_price)
            
            if success:
                # 计算手续费
                commission_cost = quantity * execution_price * self.commission
                risk_mgr.current_capital -= commission_cost
                
                return {
                    'symbol': symbol,
                    'action': 'SELL',
                    'quantity': quantity,
                    'price': execution_price,
                    'commission': commission_cost,
                    'timestamp': timestamp,
                    'type': action,
                    'confidence': 1.0
                }
            
            return None
            
        except Exception as e:
            logger.error(f"执行止损止盈交易失败: {e}")
            return None
    
    def _calculate_results(self, equity_curve: List[float], daily_returns: List[float], 
                          trades: List[Dict], timestamps: pd.DatetimeIndex) -> BacktestResult:
        """计算回测结果"""
        try:
            if not equity_curve or not daily_returns:
                raise ValueError("没有足够的数据计算结果")
            
            # 转换为pandas对象
            equity_series = pd.Series(equity_curve, index=timestamps[:len(equity_curve)])
            returns_series = pd.Series(daily_returns)
            
            # 基本指标
            total_return = (equity_curve[-1] - self.initial_capital) / self.initial_capital
            
            # 年化收益率
            days = len(equity_curve) / (252 * 6.5 * 60)  # 假设分钟数据
            annual_return = (1 + total_return) ** (1 / days) - 1 if days > 0 else 0
            
            # 夏普比率
            if len(returns_series) > 1 and returns_series.std() > 0:
                sharpe_ratio = returns_series.mean() / returns_series.std() * np.sqrt(252)
            else:
                sharpe_ratio = 0
            
            # 最大回撤
            peak = equity_series.expanding().max()
            drawdown = (equity_series - peak) / peak
            max_drawdown = abs(drawdown.min())
            
            # 交易统计
            total_trades = len(trades)
            winning_trades = len([t for t in trades if self._calculate_trade_pnl(t) > 0])
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            
            # 盈亏比
            winning_pnl = sum([self._calculate_trade_pnl(t) for t in trades if self._calculate_trade_pnl(t) > 0])
            losing_pnl = abs(sum([self._calculate_trade_pnl(t) for t in trades if self._calculate_trade_pnl(t) < 0]))
            profit_factor = winning_pnl / losing_pnl if losing_pnl > 0 else float('inf')
            
            # 平均交易收益
            avg_trade_return = sum([self._calculate_trade_pnl(t) for t in trades]) / total_trades if total_trades > 0 else 0
            
            # 波动率
            volatility = returns_series.std() * np.sqrt(252) if len(returns_series) > 1 else 0
            
            # 卡尔玛比率
            calmar_ratio = annual_return / max_drawdown if max_drawdown > 0 else 0
            
            return BacktestResult(
                total_return=total_return,
                annual_return=annual_return,
                sharpe_ratio=sharpe_ratio,
                max_drawdown=max_drawdown,
                win_rate=win_rate,
                profit_factor=profit_factor,
                total_trades=total_trades,
                avg_trade_return=avg_trade_return,
                volatility=volatility,
                calmar_ratio=calmar_ratio,
                equity_curve=equity_series,
                trades=trades,
                daily_returns=returns_series
            )
            
        except Exception as e:
            logger.error(f"计算回测结果失败: {e}")
            raise
    
    def _calculate_trade_pnl(self, trade: Dict[str, Any]) -> float:
        """计算交易盈亏"""
        # 这是一个简化版本，实际应该考虑开仓和平仓的配对
        return 0.0  # 占位符
    
    def plot_results(self, result: BacktestResult, save_path: str = None):
        """绘制回测结果"""
        try:
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            
            # 权益曲线
            axes[0, 0].plot(result.equity_curve.index, result.equity_curve.values)
            axes[0, 0].set_title('权益曲线')
            axes[0, 0].set_ylabel('组合价值')
            
            # 日收益率分布
            axes[0, 1].hist(result.daily_returns, bins=50, alpha=0.7)
            axes[0, 1].set_title('日收益率分布')
            axes[0, 1].set_xlabel('收益率')
            
            # 回撤
            peak = result.equity_curve.expanding().max()
            drawdown = (result.equity_curve - peak) / peak
            axes[1, 0].fill_between(drawdown.index, drawdown.values, 0, alpha=0.3, color='red')
            axes[1, 0].set_title('回撤')
            axes[1, 0].set_ylabel('回撤比例')
            
            # 月度收益
            monthly_returns = result.equity_curve.resample('M').last().pct_change().dropna()
            axes[1, 1].bar(range(len(monthly_returns)), monthly_returns.values)
            axes[1, 1].set_title('月度收益')
            axes[1, 1].set_ylabel('收益率')
            
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path)
            else:
                plt.show()
                
        except Exception as e:
            logger.error(f"绘制结果失败: {e}")
    
    def generate_report(self, result: BacktestResult) -> str:
        """生成回测报告"""
        try:
            report = f"""
回测报告
========

基本指标:
- 总收益率: {result.total_return:.2%}
- 年化收益率: {result.annual_return:.2%}
- 夏普比率: {result.sharpe_ratio:.2f}
- 最大回撤: {result.max_drawdown:.2%}
- 波动率: {result.volatility:.2%}
- 卡尔玛比率: {result.calmar_ratio:.2f}

交易统计:
- 总交易次数: {result.total_trades}
- 胜率: {result.win_rate:.2%}
- 盈亏比: {result.profit_factor:.2f}
- 平均交易收益: {result.avg_trade_return:.2f}

风险指标:
- 最大回撤: {result.max_drawdown:.2%}
- 波动率: {result.volatility:.2%}
- VaR (95%): 待计算

            """
            
            return report
            
        except Exception as e:
            logger.error(f"生成报告失败: {e}")
            return "报告生成失败"


# 全局回测引擎实例
backtest_engine = BacktestEngine()
